<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>反馈按钮布局测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .card-demo {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background: white;
        }
        
        .debug_info_wrapper {
            background: #f8fafd;
            border-radius: 4px;
            width: 100%;
            padding: 3px 3px;
            cursor: default;
            overflow-x: scroll;
            list-style: none;
            margin: 0;
            padding: 8px;
        }
        
        .debug_info_wrapper li {
            display: inline-block;
            width: 100%;
            font-size: 12px;
            font-weight: 400;
            color: #7c8198;
            line-height: 17px;
            white-space: nowrap;
            margin-bottom: 4px;
        }
        
        .debug_info_wrapper li.feedback {
            height: 20px;
            border-top: 1px solid #ccc;
            display: flex;
            align-items: center;
            padding-top: 4px;
        }
        
        .debug_info_wrapper li.feedback .name {
            flex-shrink: 0;
            margin-right: 8px;
            color: #4b72ef;
        }
        
        .debug_info_wrapper li.feedback > span:last-child {
            display: flex;
            align-items: center;
            flex-shrink: 0;
            gap: 8px;
        }
        
        .feedback-btn {
            width: 18px;
            height: 18px;
            cursor: pointer;
            flex-shrink: 0;
            background: #4b72ef;
            border-radius: 2px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        .feedback-btn.active {
            background: #f53c54;
        }
        
        .feedback-btn.cai-btn {
            transform: rotate(180deg);
        }
        
        .narrow-container {
            max-width: 300px;
        }
        
        .very-narrow-container {
            max-width: 200px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>反馈按钮布局测试</h1>
        
        <h2>正常宽度容器</h2>
        <div class="card-demo">
            <ul class="debug_info_wrapper">
                <li>
                    <span class="name">id：</span>
                    <span>12345</span>
                </li>
                <li>
                    <span class="name">得分（位次）：</span>
                    <span>0.8195517 (2)</span>
                </li>
                <li class="feedback">
                    <span class="name">反馈</span>
                    <span>
                        <div class="feedback-btn">👍</div>
                        <div class="feedback-btn cai-btn">👍</div>
                    </span>
                </li>
            </ul>
        </div>
        
        <h2>窄容器测试</h2>
        <div class="card-demo narrow-container">
            <ul class="debug_info_wrapper">
                <li>
                    <span class="name">id：</span>
                    <span>12345</span>
                </li>
                <li>
                    <span class="name">得分（位次）：</span>
                    <span>0.8195517 (2)</span>
                </li>
                <li class="feedback">
                    <span class="name">反馈</span>
                    <span>
                        <div class="feedback-btn">👍</div>
                        <div class="feedback-btn cai-btn">👍</div>
                    </span>
                </li>
            </ul>
        </div>
        
        <h2>极窄容器测试</h2>
        <div class="card-demo very-narrow-container">
            <ul class="debug_info_wrapper">
                <li>
                    <span class="name">id：</span>
                    <span>12345</span>
                </li>
                <li>
                    <span class="name">得分（位次）：</span>
                    <span>0.8195517 (2)</span>
                </li>
                <li class="feedback">
                    <span class="name">反馈</span>
                    <span>
                        <div class="feedback-btn active">👍</div>
                        <div class="feedback-btn cai-btn">👍</div>
                    </span>
                </li>
            </ul>
        </div>
        
        <h2>修复说明</h2>
        <div style="background: #f0f8ff; padding: 15px; border-radius: 4px; margin-top: 20px;">
            <h3>问题原因：</h3>
            <ul>
                <li>原来的反馈区域使用了 <code>white-space: nowrap</code> 但没有合适的flex布局</li>
                <li>点赞点踩按钮使用了固定的 <code>margin-left: 28px</code>，在窄容器中容易换行</li>
                <li>缺乏flex布局来确保按钮在一行显示</li>
            </ul>
            
            <h3>修复方案：</h3>
            <ul>
                <li>为 <code>.feedback</code> 添加 <code>display: flex</code> 和 <code>justify-content: space-between</code></li>
                <li>为反馈按钮容器添加 <code>display: flex</code> 和 <code>gap: 8px</code></li>
                <li>移除按钮的 <code>margin-left</code>，添加 <code>flex-shrink: 0</code> 防止压缩</li>
                <li>确保"反馈"标签和按钮都不会被压缩</li>
            </ul>
        </div>
    </div>
</body>
</html>
