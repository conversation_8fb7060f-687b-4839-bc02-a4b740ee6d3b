/*
 * @Description: 性能测试任务管理接口
 */

import axios from './axios';

// 接口基础路径
const BASE_URL = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/performance-mission`;

/**
 * 性能测试任务创建/更新DTO
 */
export interface PerformanceMissionCreateDTO {
  id?: string;
  name: string;
  description?: string;
  querySetId: string;
  strategyIds: string[];
  regionIds: string[];
  [key: string]: any;
}

/**
 * 性能测试任务查询DTO
 */
export interface PerformanceMissionQueryDTO {
  name?: string;
  status?: number;
  createUser?: string;
  startTime?: string;
  endTime?: string;
  [key: string]: any;
}

/**
 * 性能测试任务删除DTO
 */
export interface PerformanceMissionDeleteDTO {
  ids: string[];
}

/**
 * 创建或更新性能测试任务
 * @param data 任务创建/更新数据
 * @returns Promise<any>
 */
export function savePerformanceMission(data: PerformanceMissionCreateDTO): Promise<any> {
  const url = `${BASE_URL}/save`;
  return axios.post(url, data);
}

/**
 * 分页查询性能测试任务
 * @param queryDTO 查询条件
 * @returns Promise<any>
 */
export function pagePerformanceMission(queryDTO: PerformanceMissionQueryDTO): Promise<any> {
  const url = `${BASE_URL}/page?page=${queryDTO.page}&size=${queryDTO.size}&sort=${queryDTO.sort}`;
  return axios.post(url, queryDTO);
}

/**
 * 批量删除性能测试任务
 * @param deleteDTO 删除数据
 * @returns Promise<any>
 */
export function deletePerformanceMission(deleteDTO: PerformanceMissionDeleteDTO): Promise<any> {
  const url = `${BASE_URL}/delete`;
  return axios.post(url, deleteDTO);
}

/**
 * 启动性能测试任务
 * @param missionId 任务ID
 * @returns Promise<any>
 */
export function startPerformanceMission(missionId: string): Promise<any> {
  const url = `${BASE_URL}/start/${missionId}`;
  return axios.get(url);
}

/**
 * 停止性能测试任务
 * @param missionId 任务ID
 * @returns Promise<any>
 */
export function stopPerformanceMission(missionId: string): Promise<any> {
  const url = `${BASE_URL}/stop/${missionId}`;
  return axios.get(url);
}

/**
 * 获取任务进度
 * @param missionId 任务ID
 * @returns Promise<MissionProgressResponse>
 */
export function getMissionProgress(missionId: string): Promise<any> {
  const url = `${BASE_URL}/progress/${missionId}`;
  return axios.get(url);
}

/**
 * 查看性能测试结果
 * @param missionId 任务ID
 * @returns Promise<PerformanceResultResponse>
 */
export function getPerformanceResult(missionId: string): Promise<any> {
  const url = `${BASE_URL}/result/${missionId}`;
  return axios.get(url);
}

/**
 * 代理获取 Prometheus 数据集合（一次性获取所有图表数据）
 * @param missionId 任务ID
 * @returns Promise<PrometheusDataResponse>
 */
export function getPrometheusProxyData(missionId: string): Promise<any> {
  const url = `${BASE_URL}/prometheus-proxy/${missionId}`;
  return axios.get(url);
}