import { RouteRecordRaw } from "vue-router";
import { dataC } from "turing-plugin";

function getFirstConsoleMenu(sideList: any[] = [], homeList: any[] = []): string {
  const homeListStr = homeList.map((item) => item.home);
  function getFirstMenu(list: any[]) {
    for (const item of list) {
      if (item.items?.length) {
        const result: string = getFirstMenu(item.items);
        if (result) return result;
      } else if (item.console && !homeListStr.includes(item.console)) {
        return item.console.replace(/^\/lynxiao\/base\/#/, "");
      }
    }
    return false;
  }
  return getFirstMenu(sideList);
}

function getFirstHomeMenu(homeList: any[] = []) {
  return homeList?.[0]?.home?.replace(/^\/lynxiao\/base\/#/, "");
}

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    redirect: () => {
      const storeVuex: any = sessionStorage.getItem("storeVuex");
      const store: any = dataC.safeObject(storeVuex);
      return getFirstConsoleMenu(store?.consoleList) || getFirstHomeMenu(store?.homeList) || "/site";
    },
  },
  {
    path: "/meta-region",
    name: "meta-region",
    meta: { label: "区域环境管理" },
    component: () => import("@/views/meta-config/meta-region/MetaRegionIndex.vue"),
  },
  {
    path: "/meta-word",
    name: "meta-word",
    meta: { label: "字典管理" },
    component: () => import("@/views/meta-config/meta-dictionary/word/MetaWordIndex.vue"),
  },
  {
    path: "/baike-entry-intervence",
    name: "baike-entry-intervence",
    meta: { label: "百科词条干预" },
    component: () => import("@/views/intervence-manage/baike-entry-intervence/indexNew.vue"),
    children: [
      {
        path: "detail",
        name: "baike-entry-intervence::detail",
        meta: { label: "详情" },
        component: () => import("@/views/intervence-manage/baike-entry-intervence/detail/index.vue"),
      },
    ],
  },
  {
    path: "/manual-intervention",
    name: "manual-intervention",
    meta: { label: "人工干预数据" },
    component: () => import("@/views/intervence-manage/manual-intervence/ManualInterventionIndex.vue"),
    children: [
      {
        path: "domain-black-log",
        name: "manual-intervention::domain-black-log",
        meta: { label: "日志" },
        component: () => import("@/views/intervence-manage/manual-intervence/domainBlack/DomainBlackLog.vue"),
      },
      {
        path: "url-black-log",
        name: "manual-intervention::url-black-log",
        meta: { label: "日志" },
        component: () => import("@/views/intervence-manage/manual-intervence/urlBlack/URLBlackLog.vue"),
      },
      {
        path: "url-black-add",
        name: "manual-intervention::url-black-add",
        meta: { label: "新增" },
        component: () => import("@/views/intervence-manage/manual-intervence/urlBlack/URLBlackAdd.vue"),
      },
    ],
  },
  {
    path: "/meta-dict",
    name: "meta-dict",
    meta: { label: "词典管理" },
    component: () => import("@/views/meta-config/meta-dictionary/dic/MetaDictionaryIndex.vue"),
  },
  // 调度组件管理
  {
    path: "/component",
    name: "component",
    meta: { label: "调度组件" },
    component: () => import("@/views/meta-config/comp/list.vue"),
    children: [
      {
        path: "version/:id",
        name: "component::version",
        meta: { label: "组件版本" },
        component: () => import("@/views/meta-config/comp/version/list.vue"),
        children: [
          {
            path: "add",
            name: "component::version::add",
            meta: { label: "创建组件版本" },
            component: () => import("@/views/meta-config/comp/version/add.vue"),
          },
        ],
      },
    ],
  },
  // 场景策略管理
  {
    path: "/scene",
    name: "scene",
    meta: { label: "场景策略" },
    component: () => import("@/views/flow-manage/scene/list.vue"),
    children: [
      {
        path: "version/:id",
        name: "scene::version",
        meta: { label: "场景策略版本" },
        component: () => import("@/views/flow-manage/scene/version/list.vue"),
      },
    ],
  },
  // 编排模板管理
  {
    path: "/template",
    name: "template",
    meta: { label: "编排模版" },
    component: () => import("@/views/flow-manage/template/list.vue"),
  },
  // 产品方案管理
  {
    path: "/product",
    name: "product",
    meta: { label: "产品方案" },
    component: () => import("@/views/flow-manage/product/list.vue"),
    children: [
      {
        path: "version/:id",
        name: "product::version",
        meta: { label: "产品方案版本" },
        component: () => import("@/views/flow-manage/product/version/list.vue"),
      },
    ],
  },
  // 业务应用管理
  {
    path: "/app",
    name: "app",
    meta: { label: "业务应用" },
    component: () => import("@/views/business-manage/app/list.vue"),
    children: [
      {
        path: "version/:id",
        name: "app::version",
        meta: { label: "业务关联产品" },
        component: () => import("@/views/business-manage/app/version/list.vue"),
      },
    ],
  },
  // 上线计划管理
  {
    path: "/online",
    name: "online",
    meta: { label: "上线计划", hiddenBreadCrumb: true },
    component: () => import("@/views/business-manage/online/index.vue"),
  },
  // 产品分流管理
  {
    path: "/traffic",
    name: "traffic",
    meta: { label: "上线分流" },
    component: () => import("@/views/business-manage/traffic/list.vue"),
  },
  // 产品分流管理
  {
    path: "/traffic-verify",
    name: "traffic-verify",
    meta: { label: "验证分流" },
    component: () => import("@/views/business-manage/traffic/list.vue"),
  },
  //分析计划
  {
    path: "/analysis-plan",
    name: "analysis-plan",
    meta: { label: "分析计划" },
    component: () => import("@/views/evaluation-manage/analysis-plan/AnalysisPlanIndex.vue"),
    children: [
      {
        path: "details",
        name: "analysis-plan::details",
        meta: { label: "计划明细" },
        component: () => import("@/views/evaluation-manage/analysis-plan/details/index.vue"),
        children: [
          {
            path: "position",
            name: "analysis-plan::details::position",
            meta: { label: "全链路定位" },
            component: () => import("@/views/evaluation-manage/analysis-plan/details/position.vue"),
          },
        ],
      },
    ],
  },
  //归因策略
  {
    path: "/attribution-strategy",
    name: "attribution-strategy",
    meta: { label: "归因策略" },
    component: () => import("@/views/evaluation-manage/attribution-strategy/Index.vue"),
    children: [
      {
        path: "details",
        name: "attribution-strategy::details",
        meta: { label: "计划明细" },
        component: () => import("@/views/evaluation-manage/attribution-strategy/details/index.vue"),
      },
    ],
  },
  {
    path: "/evaluation-reslut",
    name: "evaluation-reslut",
    meta: { label: "结果统计" },
    component: () => import("@/views/evaluation-manage/result/index.vue"),
    children: [
      {
        path: "details",
        name: "evaluation-reslut::details",
        meta: { label: "结果分析明细" },
        component: () => import("@/views/evaluation-manage/result/detail.vue"),
      },
    ],
  },
  {
    path: "/standard-dim-group",
    name: "standard-dim-group",
    meta: { label: "维度标准" },
    component: () => import("@/views/standard/standardGroupIndex.vue"),
    children: [
      {
        path: "details",
        name: "standard-dim-group::details",
        meta: { label: "维度列表" },
        component: () => import("@/views/standard/group-list.vue"),
      },
    ],
  },
  {
    path: "/mark-standard",
    name: "mark-standard",
    meta: { label: "测评标准" },
    component: () => import("@/views/standard/markStandardIndex.vue"),
    children: [
      {
        path: "details",
        name: "mark-standard::details",
        meta: { label: "标准明细" },
        component: () => import("@/views/standard/standard-list.vue"),
      },
    ],
  },
  // 标注页面
  {
    path: "/mark-index",
    name: "mark-index",
    meta: { label: "标注", hiddenBreadCrumb: true },
    component: () => import("@/views/evaluation-manage/mark/MarkIndex.vue"),
  },
  // 拓展字段配置
  {
    path: "/field-setting",
    name: "field-setting",
    meta: { label: "拓展字段配置" },
    component: () => import("@/views/evaluation-setting/fields-setting/FieldsSetting.vue"),
  },
  // 策略字段配置 - （关注节点配置）
  {
    path: "/concern-node-setting",
    name: "concern-node-setting",
    meta: { label: "策略字段配置" },
    component: () => import("@/views/evaluation-setting/concern-node-setting/ConcernNodeSetting.vue"),
  },
  // 体验产品配置
  {
    path: "/exp-prod-setting",
    name: "exp-prod-setting",
    meta: { label: "体验产品配置" },
    component: () => import("@/views/evaluation-setting/exp-prod-setting/ExpProdSetting.vue"),
  },
  {
    path: "/mark-index/position",
    name: "mark-index::position",
    meta: { label: "全链路定位", hiddenBreadCrumb: true },
    component: () => import("@/views/evaluation-manage/mark/MarkPosition.vue"),
  },
  //体验页面
  {
    path: "/experience-index",
    name: "experience-index",
    meta: { label: "体验", hiddenBreadCrumb: true },
    component: () => import("@/views/evaluation-manage/mark/ExperienceIndex.vue"),
  },
  {
    path: "/experience-index/position",
    name: "experience-index::position",
    meta: { label: "全链路定位", hiddenBreadCrumb: true },
    component: () => import("@/views/evaluation-manage/mark/ExperiencePosition.vue"),
  },
  // 搜索请求
  {
    path: "/search-request",
    name: "search-request",
    meta: { label: "搜索请求-线上" },
    component: () => import("@/views/evaluation-manage/search-request/index.vue"),
    children: [
      {
        path: "position",
        name: "search-request::position",
        meta: { label: "全链路定位" },
        component: () => import("@/views/evaluation-manage/search-request/position.vue"),
      },
    ],
  },
  // 搜索请求-线下
  {
    path: "/search-request-debug",
    name: "search-request-debug",
    meta: { label: "搜索请求-线下" },
    component: () => import("@/views/evaluation-manage/search-request/debug.vue"),
    children: [
      {
        path: "position",
        name: "search-request-debug::position",
        meta: { label: "全链路定位" },
        component: () => import("@/views/evaluation-manage/search-request/position.vue"),
      },
    ],
  },
  // 运营可视化
  {
    path: "/business-traffic-analysis",
    name: "business-traffic-analysis",
    meta: { label: "业务应用流量分析" },
    component: () => import("@/views/operational-visibility/business-traffic/index.vue"),
  },
  {
    path: "/environmental-traffic-analysis",
    name: "environmental-traffic-analysis",
    meta: { label: "环境流量分析" },
    component: () => import("@/views/operational-visibility/environmental-traffic/index.vue"),
  },
  {
    path: "/point-traffic-anlaysis",
    name: "point-traffic-anlaysis",
    meta: { label: "埋点流量分析" },
    component: () => import("@/views/operational-visibility/point-traffic-anlaysis/index.vue"),
  },
  {
    path: "/report-anlaysis",
    name: "report-anlaysis",
    meta: { label: "报表分析" },
    component: () => import("@/views/operational-visibility/report-anlaysis/index.vue"),
    children: [
      {
        path: "details",
        name: "report-anlaysis::details",
        meta: { label: "业务应用流量汇总表" },
        component: () => import("@/views/operational-visibility/report-anlaysis/table.vue"),
      },
    ],
  },
  // 测评任务
  {
    path: "/evaluation-task",
    name: "evaluation-task",
    meta: { label: "测评任务" },
    component: () => import("@/views/evaluation-task-manage/evaluation-task/index.vue"),

    children: [
      {
        path: "details",
        name: "evaluation-task::details",
        meta: { label: "计划明细" },
        component: () => import("@/views/evaluation-task-manage/evaluation-task/details/index.vue"),
        children: [
          {
            path: "tag-details",
            name: "evaluation-task::details::tag-details",
            meta: { label: "query标注明细" },
            component: () => import("@/views/evaluation-task-manage/evaluation-task/details/tag-details.vue"),
          },
        ],
      },
    ],
  },
  // query集
  {
    path: "/query-set",
    name: "query-set",
    meta: { label: "query集" },
    component: () => import("@/views/evaluation-task-manage/query-set/index.vue"),

    children: [
      {
        path: "details",
        name: "query-set::details",
        meta: { label: "计划明细" },
        component: () => import("@/views/evaluation-task-manage/query-set/details/index.vue"),
        children: [
          {
            path: "position",
            name: "query-set::details::position",
            meta: { label: "全链路定位" },
            component: () => import("@/views/evaluation-task-manage/query-set/details/position.vue"),
          },
        ],
      },
    ],
  },
  {
    path: "/cloudfunc",
    name: "cloudfunc",
    meta: { label: "云函数管理" },
    component: () => import("@/views/cloudfunc/list/index.vue"),
    children: [
      {
        path: "version",
        name: "cloudfunc::version",
        meta: { label: "版本管理" },
        component: () => import("@/views/cloudfunc/version/index.vue"),
      },
    ],
  },
  {
    path: "/my-mission",
    name: "my-mission",
    meta: { label: "我的任务" },
    component: () => import("@/views/evaluation-manage/mission/MyMission.vue"),
  },
  {
    path: "/consistency-test",
    name: "consistency-test",
    meta: { label: "一致性测试" },
    component: () => import("@/views/evaluation-test/consistency/index.vue"),
    children: [
      {
        path: "detail",
        name: "consistency-test::detail",
        meta: { label: "任务详情" },
        component: () => import("@/views/evaluation-test/consistency/detail.vue"),
      },
    ],
  },
  {
    path: "/performance-test",
    name: "performance-test",
    meta: { label: "性能测试" },
    component: () => import("@/views/evaluation-test/performance/index.vue"),
    children: [
      {
        path: "detail",
        name: "performance-test::detail",
        meta: { label: "任务详情" },
        component: () => import("@/views/evaluation-test/performance/detail.vue"),
      },
    ],
  },
];

export default routes;
