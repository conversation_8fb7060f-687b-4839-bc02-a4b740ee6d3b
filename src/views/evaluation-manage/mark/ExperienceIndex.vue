<template>
  <page-wrapper :route-name="`experience-index::`">
    <el-dialog title="全链路" v-model="allChainVisible" fullscreen class="position-dialog">
      <template #header> <el-button type="primary" @click="allChainVisible = false"
          :icon="icons.Back">返回</el-button><Strong></Strong> </template>
      <ExperiencePosition ref="experiencePositionRef"></ExperiencePosition>
    </el-dialog>

    <div class="experience-index">
      <el-row style="padding-bottom: 8px;">
        <el-col :span="24">
          <!-- 第一行:搜索框、设置和更多操作 -->
          <div class="header-row">
            <el-input v-model="modelValue.search" placeholder="请输入你要测评的query" :prefix-icon="icons.Search"
              @keydown.enter="events.doExperience" style="flex: 1; margin-right: 12px;">
              <template #append>
                <el-button @click="events.doExperience">搜索</el-button>
              </template>
            </el-input>

            <el-popover ref="popover" :hide-after="0" placement="bottom" :width="600" :visible="popoverVisible">
              <template #reference>
                <el-button type="" circle style="margin-right: 8px;" :icon="icons.Setting"
                  :class="{ 'setting-active': popoverVisible }" @click="openSettingPopover"></el-button>
              </template>
              <el-row>
                <el-col :span="13" style="align-content: center;">
                  <strong style="font-size: 14px;">字段展示配置</strong>
                </el-col>
                <el-col :span="5">
                  <my-button @click="clearSelectedField()" style="margin-bottom: 3px;" type="priamry"
                    primary>清空已选字段</my-button>
                </el-col>
                <el-col :span="4" style="text-align: right;">
                  <el-dropdown @mousedown.stop @click.stop @command="changeFieldCategory">
                    <el-button>
                      快速选择类别
                      <el-icon size="18px">
                        <ArrowDown />
                      </el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item v-for="item in fieldCategoryOptions" :command="item">{{ item.label }}</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </el-col>
                <el-col :span="2">
                </el-col>
              </el-row>
              <el-row>
                <el-checkbox-group v-model="extendFieldsTemp" style="width: 100%;display: flex;flex-wrap: wrap;">
                  <el-checkbox v-for="item in fieldList" :label="item.field" :key="item.field" :value="item.field"
                    style="margin-right: 10px;margin-bottom: 10px;">
                    {{ item.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-row>
            </el-popover>

            <el-dropdown trigger="click">
              <el-button type="primary">更多操作</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :icon="icons.Open" @click="modelValue.displayScore = true"
                    :disabled="modelValue.displayScore == true">
                    显示策略数据
                  </el-dropdown-item>
                  <el-dropdown-item :icon="icons.TurnOff" @click="modelValue.displayScore = false"
                    :disabled="modelValue.displayScore == false">
                    隐藏策略数据
                  </el-dropdown-item>
                  <el-dropdown-item :icon="icons.ArrowDownBold" @click="events.unfoldContent"
                    :disabled="dataC.isEmpty(modelValue.recall)">
                    展开content
                  </el-dropdown-item>
                  <el-dropdown-item :icon="icons.ArrowUpBold" @click="events.foldContent"
                    :disabled="dataC.isEmpty(modelValue.recall)">
                    折叠content
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <div class="flexBetween">
            <ProcessBox 
              label="A" 
              :region-list="modelValue.regionList"
              :markRecordId="modelValue.markRecordId" 
              :processName="modelValue.processA.name"
              :processRegion="modelValue.processA.region"
              :isEditInputArgs="modelValue.processA.isEditInputArgs"
              :hasValue="!dataC.isEmpty(modelValue.processA.processId)" 
              :flowList="modelValue.flowList"
              :flowCode="modelValue.flowSelectA"
              :sceneList="modelValue.sceneList"
              @clear="events.clearProcess('A')" 
              @editParams="events.editInputArgs('A')"
              @flowChange="events.handleFlowChange('A', $event)"
              @regionChange="events.handleRegionChange('A', $event)" />

            <div class="second">
              <FlowParam v-if="modelValue.processA.isEditInputArgs || modelValue.processB.isEditInputArgs"
                :key="modelValue.paramUseKey"
                :inputArgs="modelValue.processA.isEditInputArgs ? modelValue.processA.inputArgs : modelValue.processB.inputArgs"
                style="width: 100%"></FlowParam>
            </div>

            <ProcessBox 
              label="B" 
              :region-list="modelValue.regionList"
              :markRecordId="modelValue.markRecordId" 
              :processName="modelValue.processB.name"
              :processRegion="modelValue.processB.region"
              :isEditInputArgs="modelValue.processB.isEditInputArgs"
              :hasValue="!dataC.isEmpty(modelValue.processB.processId)" 
              :flowList="modelValue.flowList"
              :flowCode="modelValue.flowSelectB"
              :sceneList="modelValue.sceneList"
              @clear="events.clearProcess('B')" 
              @editParams="events.editInputArgs('B')"
              @flowChange="events.handleFlowChange('B', $event)"
              @regionChange="events.handleRegionChange('B', $event)" />
          </div>
        </el-col>
      </el-row>

      <el-row style="flex-grow: 1; overflow: hidden;">
        <el-tabs v-model="modelValue.activeName" @tab-change="events.tabChange" style="width: 100%">
          <el-tab-pane label="搜索结果" name="searchPane">
            <my-empty v-if="dataC.isEmpty(modelValue.searchResult)" :size="120" class="searchPane" />
            <el-row v-if="!dataC.isEmpty(modelValue.searchResult)" :gutter="8" class="searchPane">
              <template v-for="(strategy, strategyIdx) in modelValue.searchResult">
                <el-col :span="multipleStrategy ? 12 : 24">
                  <div class="mark-pane">
                    <div v-if="strategy.traceId">
                      <span style="font-weight: bold; font-size: 12px">traceId：</span><span>{{ strategy.traceId
                        }}&nbsp;&nbsp;</span>
                      <el-icon class="icon-copy" @click="copyText(strategy.traceId)">
                        <CopyDocument />
                      </el-icon>
                    </div>
                    <MarkItem v-for="(doc, docIdx) in strategy.recallList"
                      :key="`markItemRef-${strategy.strategyId}-${docIdx}`" :extend-fields="extendFields"
                      v-model="strategy.recallList[docIdx].markResult"
                      :ref="`markItemRef-${strategy.strategyId}-${docIdx}`" :dimsInfo="[]" :docIndex="docIdx"
                      :recallInfo="doc" :displayScore="modelValue.displayScore" :markRecordId="modelValue.markRecordId"
                      :targetId="strategy.targetId" :tags="strategyIdx == 0 ? 'A' : 'B'"></MarkItem>
                  </div>
                </el-col>
              </template>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="Chat效果" name="chatPane">
            <my-empty v-if="dataC.isEmpty(modelValue.searchResult)" :size="120" class="chatPane" />
            <el-row v-if="!dataC.isEmpty(modelValue.searchResult)" :gutter="8" class="chatPane">
              <template v-for="(strategy, strategyIdx) in modelValue.searchResult">
                <el-col :span="multipleStrategy ? 12 : 24">
                  <div class="flex">
                    <strong style="margin-right: 5px; color: red">结果：</strong>
                  </div>
                  <div class="chat-pane">
                    <MarkChat :chat="strategy.chat || {}" :recallInfo="strategy.recallList"
                      @reloadChat="events.reloadChat(strategy.metadata.tag)"></MarkChat>
                  </div>
                </el-col>
              </template>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-row>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed, onMounted, nextTick } from "vue";
import * as icons from "@element-plus/icons-vue";
import { keys, cloneDeep, result } from "lodash";
import { dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import MarkItem from "./MarkItem.vue";
import MarkChat from "./MarkChat.vue";
import FlowParam from "./FlowParam.vue";
import ExperiencePosition from "./ExperiencePosition.vue";
import * as markApi from "@/api/eval-mark";
import * as sceneApi from "@/api/scene";
import * as commonApi from "@/api/common";
import { copyText, getText } from "@/utils/helpers";
import { Plus, Close } from "@element-plus/icons-vue";
import ProcessBox from "./ProcessBox.vue";
import * as evalSettingApi from "@/api/eval-setting";

interface Process {
  name: string;
  processId: string;
  region: string;
  inputArgs: Array<{ key: string; value: any }>;
  isEditInputArgs: boolean;
  metadata: {
    tag: string;
  };
}

interface Scene {
  name: string;
  processId: string;
  version: string;
}

interface Region {
  name: string;
  code: string;
}

interface FlowItem {
  icon: string;
  name: string;
  code: string;
  sort?: number;
}

interface SearchResult {
  traceId?: string;
  chat?: {
    traceId: string;
    [key: string]: any;
  };
  recallList: Array<any>;
  metadata: {
    tag: string;
  };
  [key: string]: any;
}

interface Chat {
  id: string;
  traceId: string;
  [key: string]: any;
}

interface ModelValue {
  traceId: string;
  flowCode: string;
  flowInput: string;
  flowList: FlowItem[];
  regionList: Region[];
  searchResult: SearchResult[];
  activeName: string;
  paramUseKey: number;
  inputArgs: Array<{ key: string; value: any }>;
  search: string;
  markRecordId: string;
  selectedProcessList: string[];
  displayScore: boolean;
  sceneList: Scene[];
  processA: Process;
  processB: Process;
  chat?: Chat;
  lastSend: Number;
  flowSelectA: string;  // 流程A下拉框显示值
  flowSelectB: string;  // 流程B下拉框显示值
}

const { $app, $router, $auth, proxy } = useCtx();
const { api } = useStore();

const iconType = ["ChatSquare", "ChatRound", "ChatLineRound"];

const modelValue = reactive<ModelValue>({
  traceId: "",
  flowCode: "",
  flowInput: "",
  flowList: [],
  regionList: [],
  searchResult: [],
  activeName: "searchPane",
  paramUseKey: 0,
  inputArgs: [],
  search: "",
  markRecordId: "",
  selectedProcessList: [],
  displayScore: false,
  sceneList: [],
  processA: {
    name: "",
    processId: "",
    region: "",  // 流程A默认空，在onMounted中设置为第一个环境
    inputArgs: [],
    isEditInputArgs: false,
    metadata: {
      tag: "A",
    },
  },
  processB: {
    name: "",
    processId: "",
    region: "",  // 流程B不设置默认环境
    inputArgs: [],
    isEditInputArgs: false,
    metadata: {
      tag: "B",
    },
  },
  lastSend: Date.now(),
  flowSelectA: "",  // 流程A下拉框显示值
  flowSelectB: "",  // 流程B下拉框显示值
});

const extendFields = ref<any[]>([]);
const extendFieldsTemp = ref<any[]>([]);
// 字段类型options
const fieldCategoryOptions = ref<any[]>([]);
// 字段列表
const fieldList = ref<any[]>([]);

// 获取字段类型列表
const getFieldCategoryList = async () => {
  try {
    const result = await evalSettingApi.findAllCategories();
    console.log("getFieldCategoryList:", result);

    if (dataC.isEmpty(result.data)) {
      fieldCategoryOptions.value = [];
      return;
    }

    fieldCategoryOptions.value = result.data.map((category: String) => {
      return {
        label: category,
        value: category
      }
    });
  } catch (error) {
    console.error('获取字段类型列表:', error);
  }
}
getFieldCategoryList();

// 获取所有的字段列表
const getFieldsList = async () => {
  try {
    const result = await evalSettingApi.list();
    fieldList.value = result.data;
    fieldList.value.map(item => {
      item.label = item.name;
      item.value = item.field;
    })
  } catch (error) {
    console.error('获取字段列表失败:', error);
  }
}
getFieldsList();

const popoverVisible = ref(false);

function openSettingPopover() {
  popoverVisible.value = !popoverVisible.value;
  if (!popoverVisible.value) {
    extendFields.value = cloneDeep(extendFieldsTemp.value);
  }
}

function clearSelectedField() {
  extendFieldsTemp.value = [];
}

function changeFieldCategory(newValue: any) {
  if (!dataC.isEmpty(newValue)) {

    const list = fieldList.value.filter(item => {
      if (item.category == newValue.label) {
        return item;
      }
    }).map(item => item.field);

    list.forEach((item) => {
      if (!extendFieldsTemp.value.includes(item)) {
        extendFieldsTemp.value.push(item);
      }
    });
  }
}


const multipleStrategy = computed(() => {
  return !dataC.isEmpty(modelValue.processA.processId) && !dataC.isEmpty(modelValue.processB.processId);
});

//全链路显示
const allChainVisible = ref(false);

//获取全链路跳转的query参数
const getAllChainToQuery = () => {
  return {
    mode: "ceping",
    chatId: modelValue.chat?.id || "",
  };
};

//获取全链路跳转url地址
const allChainTo = computed(() => {
  const query = getAllChainToQuery();
  const routerQuery = new URLSearchParams();
  (Object.keys(query) as Array<keyof typeof query>).forEach((key) => {
    routerQuery.append(key, String(query[key]));
  });
  return `/experience-index/position?${routerQuery.toString()}`;
});

const events = reactive({
  //搜索和chat面板切换时
  tabChange: (_tabPaneName: string) => {
    // 面板切换逻辑
  },
  //全链路
  allChain: async (e?: Event) => {
    //阻止原生路由跳转事件
    if (e) {
      e.preventDefault();
    }
    //修改当前路由
    await $router.push({
      name: `experience-index`,
      query: getAllChainToQuery(),
    });
    //打开一个全屏dialog
    allChainVisible.value = true;
    //刷新全链路
    nextTick(() => {
      proxy.$refs.experiencePositionRef.getTraceinfo();
    });
  },
  //展开content
  unfoldContent: () => {
    modelValue.searchResult.forEach((strategy: SearchResult) => {
      strategy.recallList.forEach((_, docIdx: number) => {
        proxy.$refs[`markItemRef-${strategy.metadata.tag}-${docIdx}`][0].unfoldContent();
      });
    });
  },
  //折叠content
  foldContent: () => {
    modelValue.searchResult.forEach((strategy: SearchResult) => {
      strategy.recallList.forEach((_, docIdx: number) => {
        proxy.$refs[`markItemRef-${strategy.metadata.tag}-${docIdx}`][0].foldContent();
      });
    });
  },
  //切换为标注模式
  changeMode: () => {
    $router.push({
      name: `mark-index`,
    });
  },

  //修改流程入参
  editInputArgs: (item: string) => {
    console.log("editInputArgs", item);

    if (item === "A") {
      modelValue.processB.isEditInputArgs = false;
      modelValue.processA.isEditInputArgs = !modelValue.processA.isEditInputArgs;
      return;
    }

    if (item === "B") {
      modelValue.processA.isEditInputArgs = false;
      modelValue.processB.isEditInputArgs = !modelValue.processB.isEditInputArgs;
      return;
    }
  },

  //获取流程动态表单参数
  getInputArgs: async (processId: string): Promise<Array<{ key: string; value: any }>> => {
    try {
      const result = await commonApi.getProcessInputArgs(processId);
      if (!result?.inputArgs) {
        $app.$message.warning("未获取到流程参数，接口返回格式异常");
        return [];
      }
      const resp = result.inputArgs.filter((item: { key: string }) => item.key !== "query");
      modelValue.paramUseKey += 1;
      return resp;
    } catch (error) {
      console.error("Failed to get input args:", error);
      $app.$message.warning("获取流程参数失败");
      return [];
    }
  },
  // 处理区域变化
  handleRegionChange: (process: 'A' | 'B', region: string) => {
    if (process === 'A') {
      modelValue.processA.region = region;
    } else {
      modelValue.processB.region = region;
    }
  },
  
  // 处理流程选择变化
  handleFlowChange: async (process: 'A' | 'B', flowData: any) => {
    const { code, isOther, customId } = flowData;
    
    if (isOther && customId) {
      // 处理"其他产品"输入的流程ID
      await events.addOtherProcessForTarget(process, customId);
      // 添加完成后，flowSelect 已在 addOtherProcessForTarget 中更新为新添加的流程
    } else if (code && code !== 'other') {
      // 处理普通流程选择
      const selectedItem = modelValue.flowList.find((item: FlowItem) => item.code === code);
      if (selectedItem) {
        if (process === 'A') {
          modelValue.processA.processId = code;
          modelValue.flowSelectA = code;  // 同步更新下拉框显示值
          await updateProcessA();
        } else {
          modelValue.processB.processId = code;
          modelValue.flowSelectB = code;  // 同步更新下拉框显示值
          await updateProcessB();
        }
      }
    } else if (code === 'other') {
      // 选择了"其他产品"，更新下拉框显示值
      if (process === 'A') {
        modelValue.flowSelectA = 'other';
      } else {
        modelValue.flowSelectB = 'other';
      }
    } else {
      // 清空选择
      if (process === 'A') {
        modelValue.flowSelectA = '';
      } else {
        modelValue.flowSelectB = '';
      }
    }
  },
  
  // 为指定Process添加自定义流程
  addOtherProcessForTarget: async (process: 'A' | 'B', flowInput: string) => {
    if (dataC.isEmpty(flowInput)) {
      $app.$message.warning("请输入流程id");
      return;
    }
    
    // 检验合法性
    const isValidProcess = modelValue.sceneList.some((scene) => scene.processId === flowInput);
    if (!isValidProcess) {
      $app.$message.warning("请输入有效的流程id");
      return;
    }

    // 查找对应的场景信息
    const targetScene = modelValue.sceneList.find((scene) => scene.processId === flowInput);
    if (!targetScene) {
      return;
    }

    // 检查是否已经存在于下拉列表中
    const existsInList = modelValue.flowList.some((item: FlowItem) => item.code === flowInput);
    
    if (!existsInList) {
      // 添加到下拉列表中（在"其他产品"之前）
      const otherIndex = modelValue.flowList.findIndex((item: FlowItem) => item.code === 'other');
      const newFlowItem: FlowItem = {
        icon: "Connection",
        name: `${targetScene.name}(V${targetScene.version})`,
        code: flowInput,
        sort: modelValue.flowList.length - 1,
      };
      
      if (otherIndex > -1) {
        modelValue.flowList.splice(otherIndex, 0, newFlowItem);
      } else {
        modelValue.flowList.push(newFlowItem);
      }
      
      $app.$message.success(`已添加流程: ${newFlowItem.name}`);
    }

    // 应用到指定Process，并更新下拉框显示为新添加的流程
    if (process === 'A') {
      modelValue.processA.processId = flowInput;
      modelValue.flowSelectA = flowInput;  // 选中新添加的流程
      await updateProcessA();
    } else {
      modelValue.processB.processId = flowInput;
      modelValue.flowSelectB = flowInput;  // 选中新添加的流程
      await updateProcessB();
    }
  },
  flowInputBlur: () => { },

  addOtherProcess: async () => {
    if (dataC.isEmpty(modelValue.flowInput)) {
      $app.$message.warning("请输入流程id");
      return;
    }
    //检验合法性
    const isValidProcess = modelValue.sceneList.some((scene) => scene.processId === modelValue.flowInput);
    if (!isValidProcess) {
      $app.$message.warning("请输入有效的流程id");
      return;
    }

    // 查找对应的场景信息
    const targetScene = modelValue.sceneList.find((scene) => scene.processId === modelValue.flowInput);
    if (!targetScene) {
      return;
    }

    // 检查是否已经存在于下拉列表中
    const existsInList = modelValue.flowList.some((item: FlowItem) => item.code === modelValue.flowInput);
    
    if (!existsInList) {
      // 添加到下拉列表中（在“其他产品”之前）
      const otherIndex = modelValue.flowList.findIndex((item: FlowItem) => item.code === 'other');
      const newFlowItem: FlowItem = {
        icon: "Connection", // 使用默认图标
        name: `${targetScene.name}(V${targetScene.version})`,
        code: modelValue.flowInput,
        sort: modelValue.flowList.length - 1, // 排在“其他产品”之前
      };
      
      if (otherIndex > -1) {
        modelValue.flowList.splice(otherIndex, 0, newFlowItem);
      } else {
        modelValue.flowList.push(newFlowItem);
      }
      
      $app.$message.success(`已添加流程: ${newFlowItem.name}`);
    }

    // 设置当前选中值
    modelValue.flowCode = modelValue.flowInput;
    modelValue.flowInput = "";
  },

  //获取chat验证结
  reloadChat: async (tagName: string) => {
    try {
      let params = {
        recall: false,
        trace: true,
        chat: true,
      };
      const result = await markApi.getMarkReocordById(modelValue.markRecordId, params);

      if (!dataC.isEmpty(modelValue.processA.processId)) {
        const response = result.data.targets.find((item: SearchResult) => item.metadata.tag === tagName);
        const searchResult = modelValue.searchResult.find((item: SearchResult) => item.metadata.tag === tagName);
        if (response && searchResult) {
          searchResult.chat = response.chat;
        }
      }
    } catch (error) {
      console.error("Failed to reload chat:", error);
      $app.$message.error("重新加载聊天失败");
    }
  },

  //单query搜索
  doExperience: async () => {

    //防止页面频繁点击
    if (isQuickClick()) {
      //频繁操作 
      return;
    }


    try {
      // 清空当前页面信息
      modelValue.traceId = "";
      modelValue.searchResult = [];

      if (dataC.isEmpty(modelValue.search)) {
        $app.$message.warning("请输入搜索内容");
        return;
      }

      // 构建流程列表
      const processList = [];

      if (!dataC.isEmpty(modelValue.processA.processId)) {
        processList.push({
          processId: modelValue.processA.processId,
          regionCode: modelValue.processA.region,
          payload: buildPayload(modelValue.processA.inputArgs),
          metadata: modelValue.processA.metadata,
        });
      }

      if (!dataC.isEmpty(modelValue.processB.processId)) {
        processList.push({
          processId: modelValue.processB.processId,
          regionCode: modelValue.processB.region,
          payload: buildPayload(modelValue.processB.inputArgs),
          metadata: modelValue.processB.metadata,
        });
      }

      if (processList.length === 0) {
        $app.$message.warning("请至少选择一个策略");
        return;
      }

      const data = {
        query: modelValue.search,
        processList: processList,
      };

      console.log("create markRecord data:", data);

      const result = await markApi.createMarkRecord(data);

      if (!result.data) {
        $app.$message.error("创建测评记录失败");
        return;
      }

      modelValue.markRecordId = result.data;
      await events.findByMarkRecordId(result.data);
    } catch (error) {
      console.error("搜索失败:", error);
      $app.$message.error("搜索失败，请稍后重试");
    }
  },

  findByMarkRecordId: async (markRecordId: string) => {
    let params = {
      recall: true,
      trace: false,
      chat: true,
    };
    await markApi.getMarkReocordById(markRecordId, params).then((result) => {
      events.render(result);
    });
  },

  // 将获取到的测评记录渲染到页面
  render: (result: any) => {
    modelValue.markRecordId = result.data.id;
    modelValue.search = result.data.query;
    //判断搜索结果是否为空
    const emptyFlag = result.data.targets.every((item: SearchResult) => dataC.isEmpty(item.recallList));
    if (emptyFlag) {
      $app.$message.warning("搜索结果为空!");
      return;
    }

    if (!dataC.isEmpty(modelValue.processA.processId)) {
      const processResult = result.data.targets.find((item: SearchResult) => item.metadata.tag === "A");
      console.log("processResult:", processResult);
      if (processResult) {
        modelValue.searchResult.push(processResult);
      }
    }

    if (!dataC.isEmpty(modelValue.processB.processId)) {
      const processResult = result.data.targets.find((item: SearchResult) => item.metadata.tag === "B");
      if (processResult) {
        modelValue.searchResult.push(processResult);
      }
    }
  },

  clearProcess: async (process: string) => {
    console.log("clear:", process);

    if (process === "A") {
      modelValue.processA.processId = "";
      modelValue.flowSelectA = "";  // 清空下拉框显示值
      await updateProcessA();
    } else if (process === "B") {
      modelValue.processB.processId = "";
      modelValue.flowSelectB = "";  // 清空下拉框显示值
      await updateProcessB();
    }

    // 从搜索结果中移除指定标签的流程结果
    modelValue.searchResult = modelValue.searchResult.filter((item: SearchResult) => item.metadata.tag !== process);
    modelValue.inputArgs = [];
  },
});

// 修改 getInputArgs 函数
const getInputArgs = async (processId: string): Promise<Array<{ key: string; value: any }>> => {
  try {
    const result = await commonApi.getProcessInputArgs(processId);
    if (!result?.inputArgs) {
      $app.$message.warning("未获取到流程参数，接口返回格式异常");
      return [];
    }
    const resp = result.inputArgs.filter((item: { key: string }) => item.key !== "query");
    modelValue.paramUseKey += 1;
    return resp;
  } catch (error) {
    console.error("Failed to get input args:", error);
    $app.$message.warning("获取流程参数失败");
    return [];
  }
};

// 优化 updateProcess 函数
const updateProcess = async (process: "A" | "B") => {
  const currentProcess = process === "A" ? modelValue.processA : modelValue.processB;
  const targetScene = modelValue.sceneList.find((item: Scene) => item.processId === currentProcess.processId);

  if (!targetScene) {
    // 清空流程时，保留环境选择
    if (process === "A") {
      modelValue.processA = {
        name: "",
        processId: "",
        region: modelValue.processA.region,  // 保留原有环境
        inputArgs: [],
        isEditInputArgs: false,
        metadata: { tag: "A" },
      };
    } else {
      modelValue.processB = {
        name: "",
        processId: "",
        region: modelValue.processB.region,  // 保留原有环境
        inputArgs: [],
        isEditInputArgs: false,
        metadata: { tag: "B" },
      };
    }
    return;
  }

  const inputArgs: Array<{ key: string; value: any }> = [];
  if (currentProcess.processId) {
    const args = await getInputArgs(currentProcess.processId);
    inputArgs.push(...args);
  }

  const updatedProcess = {
    name: `${targetScene.name}(V${targetScene.version})`,
    processId: targetScene.processId,
    inputArgs: inputArgs,
    region: currentProcess.region || (process === "A" && modelValue.regionList.length > 0 ? modelValue.regionList[0].code : ""),  // 保留原有环境，流程A首次选择时使用默认值
    isEditInputArgs: currentProcess.isEditInputArgs,
    metadata: { tag: process },
  };

  if (process === "A") {
    modelValue.processA = updatedProcess;
  } else {
    modelValue.processB = updatedProcess;
  }
};

// 替换原来的 updateProcessA 和 updateProcessB
const updateProcessA = () => updateProcess("A");
const updateProcessB = () => updateProcess("B");

// 优化 doExperience 函数中的 payload 构建
const buildPayload = (inputArgs: Array<{ key: string; value: any }>) => {
  const payload: Record<string, any> = {};
  inputArgs.forEach((item) => {
    if (item.key && item.value !== undefined) {
      payload[item.key] = item.value;
    }
  });
  return payload;
};

/**
 * 判断是否为快速点击  true：为快速点击 应禁止    false: 否， 可继续下一步操作
 */
function isQuickClick() {
  const now = Date.now();
  if (now - modelValue.lastSend > 2000) { // 2000ms 内只触发一次
    modelValue.lastSend = now;
    return false;
  } else {
    return true;
  }
}

onMounted(async () => {
  //区域
  const regionList = await api.getMetaRegionList();
  modelValue.regionList = regionList as Region[];
  
  // 流程A默认选择第一个环境
  if (modelValue.regionList.length > 0) {
    modelValue.processA.region = modelValue.regionList[0].code;
  }
  // 流程B不设置默认环境，保持为空

  //所有流程
  const sceneListResponse = await sceneApi.getSceneVersionListByName();
  modelValue.sceneList = sceneListResponse.data as Scene[];

  //根据全局字典获取获取流程id列表
  const evalExpProd = (await evalSettingApi.listExpProd().then(result => result.data)) as FlowItem[];
  //根据sort字段排序
  evalExpProd.sort((a, b) => (a.sort || 0) - (b.sort || 0));
  //目前图标是从这个页面写死，然后取余获取图标
  modelValue.flowList = evalExpProd.map((item: FlowItem, index: number) => {
    const { icon, ...rest } = item;
    return {
      icon: iconType[index % iconType.length],
      ...rest,
    };
  });
  //流程支持用户自定义
  modelValue.flowList.push({
    icon: "Connection",
    name: "其他产品",
    code: "other",
  });

  // 初始化 processA 和 processB（在设置默认环境之后）
  await updateProcessA();
  await updateProcessB();
  
  // 设置默认选中第一个流程并触发相应效果
  if (modelValue.flowList.length > 0 && modelValue.flowList[0].code !== 'other') {
    const firstFlow = modelValue.flowList[0];
    modelValue.flowCode = firstFlow.code;
    // 触发flowCodeChange事件，获取流程参数等
    await events.flowCodeChange(firstFlow);
  } else {
    modelValue.flowCode = modelValue.flowList[0]?.code || '';
  }

});

</script>

<style lang="scss" scoped>
.experience-index {
  display: flex;
  flex-direction: column;
  padding: 8px 15px;
  line-height: 23px;

  .setting-active {
    border-color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
  }

  .header-row {
    display: flex;
    align-items: center;
    gap: 0;
    margin-bottom: 12px;
  }

  .first.flex {
    display: flex;
    align-items: center;

    ::v-deep .el-link__inner {
      white-space: nowrap;
    }

    .flow-code {
      overflow-x: hidden;
    }

    .flow-code:hover {
      overflow-x: auto;
    }

    div+div {
      margin-left: 12px;
    }
  }

  .flexBetween {
    >.flex {
      div+div {
        margin-left: 12px;
      }
    }
  }

  .searchPane {
    height: 100%;
    overflow-y: auto;

    ::v-deep .el-col {
      height: 100%;

      .mark-pane {
        height: 100%;
        padding: 0 6px;
        overflow-y: auto;
      }
    }
  }

  .chatPane {
    height: 100%;

    ::v-deep .el-col {
      height: 100%;

      .chat-pane {
        height: calc(100% - 40px);
        overflow-y: auto;
        padding: 2px 3px;
      }
    }
  }

  ::v-deep {
    .icon-copy {
      color: $primary-color;
      cursor: pointer;
    }
  }

  ::v-deep .el-tabs--top {
    height: 100%;
  }

  ::v-deep .el-tab-pane {
    height: 100%;
  }

  ::v-deep .el-input-group__append {
    background-color: var(--el-color-primary);
    color: #fff;
  }

  .empty-pane,
  .mark-pane,
  .chat-pane {
    height: 100%;
    overflow-y: auto;
  }

  .mark-pane {
    padding: 0 8px;
  }

  .second {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

::v-deep .el-dropdown-menu__item {
  flex-direction: row !important;
  align-items: center !important;
}
</style>
<style lang="scss">
.position-dialog {
  .el-dialog__header {
    padding: 0;
  }

  .el-dialog__body {
    height: calc(100vh - 72px);
    padding: 0;
  }

  .container {
    height: 100%;
    padding: 0;
  }
}
</style>