<template>
  <el-dialog title="全链路" v-model="allChainVisible" fullscreen class="position-dialog">
    <template #header> <el-button type="primary" @click="closeTraceLogs" :icon="Back">返回</el-button><Strong></Strong> </template>
    <ExperiencePosition ref="markPositionRef"></ExperiencePosition>
  </el-dialog>
  <div class="process-box">
    <div class="label" style="font-size: 16px; font-weight: bold; margin-bottom: 8px;">流程{{ label }}</div>
    
    <!-- 环境选择下拉框 -->
    <el-select 
      :model-value="processRegion" 
      placeholder="请选择区域"
      @change="handleRegionChange"
      @click.stop
      size="small"
      style="width: 100%; margin-bottom: 8px;">
      <el-option
        v-for="item in regionList"
        :key="item.code"
        :label="item.name"
        :value="item.code">
      </el-option>
    </el-select>
    
    <!-- 产品流程选择下拉框 -->
    <el-select 
      :model-value="flowCode" 
      placeholder="请选择产品流程"
      filterable
      clearable
      @change="handleFlowChange"
      @click.stop
      size="small"
      style="width: 100%; margin-bottom: 8px;">
      <el-option
        v-for="item in flowList"
        :key="item.code"
        :label="item.name"
        :value="item.code">
        <span>{{ item.name }}</span>
      </el-option>
    </el-select>

    <!-- 其他产品输入框 -->
    <el-input 
      v-if="showOtherInput"
      v-model="otherFlowInput"
      placeholder="请输入产品流程ID"
      size="small"
      @click.stop
      @keydown.enter="addOtherProcess"
      style="margin-bottom: 8px;">
      <template #append>
        <el-button @click.stop="addOtherProcess" size="small">添加</el-button>
      </template>
    </el-input>

    <el-tooltip :content="processName" placement="top" :show-after="500" :hide-after="0">
      <div class="process-name">{{ processName }}</div>
    </el-tooltip>

    <div class="flexBetween" style="margin-top: 8px">
      <router-link v-if="processName" :to="allChainTo" style="margin-left: 1px" @click.stop>
        <el-button size="small" round @click.stop="events.allChain" :disabled="dataC.isEmpty(markRecordId)">查看全链路</el-button>
      </router-link>
      <el-button v-if="processName" class="edit-params-btn" size="small" round :type="isEditInputArgs ? 'primary' : 'pl'" @click.stop="$emit('editParams')">
        修改参数
      </el-button>
    </div>
    
    <el-button v-show="hasValue" class="clear-btn" size="small" @click.stop="$emit('clear')">
      清空
    </el-button>
  </div>
</template>

<script lang="ts" setup>
import ExperiencePosition from "./ExperiencePosition.vue";
import useCtx from "@/hooks/useCtx";
import { keys} from "lodash";
import { dataC } from "turing-plugin";
import { ref, reactive, nextTick, computed } from "vue";
const { $app, $router, proxy } = useCtx();

const props = defineProps({
  label: {
    type: String,
    required: true,
  },
  regionList:{
    type: Array as any,
    default: [],
  },
  processRegion: {
    type: String,
    default: "",
  },
  markRecordId:{
    type: String,
    default: true,
  },
  processName: {
    type: String,
    default: "",
  },
  isEditInputArgs: {
    type: Boolean,
    default: false,
  },
  hasValue: {
    type: Boolean,
    default: false,
  },
  flowList: {
    type: Array as any,
    default: () => [],
  },
  flowCode: {
    type: String,
    default: "",
  },
  sceneList: {
    type: Array as any,
    default: () => [],
  },
});

const emit = defineEmits(["clear", "editParams", "flowChange", "regionChange"]);

const otherFlowInput = ref("");
const showOtherInput = computed(() => props.flowCode === 'other');

// 处理区域选择变化
const handleRegionChange = (region: string) => {
  emit('regionChange', region);
};

// 处理流程选择变化
const handleFlowChange = (code: string) => {
  if (!code) {
    // 清空选择
    emit('flowChange', { code: '', isOther: false });
    return;
  }
  
  if (code === 'other') {
    // 选择了"其他产品"，不改变选中状态，保持"other"选中以便继续添加
    emit('flowChange', { code: 'other', isOther: true });
  } else {
    // 选择了普通流程
    emit('flowChange', { code, isOther: false });
  }
};

// 添加其他产品
const addOtherProcess = () => {
  if (!otherFlowInput.value) {
    $app.$message.warning("请输入流程ID");
    return;
  }
  
  emit('flowChange', { 
    code: 'other', 
    isOther: true, 
    customId: otherFlowInput.value 
  });
  
  otherFlowInput.value = "";
};

//全链路显示
const allChainVisible = ref(false);

function closeTraceLogs(){
  allChainVisible.value = false;
  $router.push({ name: `experience-index`});
}

const regionName = computed(()=>{
  return dataC.getItemByValue(props.regionList, props.processRegion, "code")["name"] || '';
});


const getAllChainToQuery = () => {
  const oldQuery = $router.currentRoute.value.query;
  return {...oldQuery, markRecordId: props.markRecordId, tag: props.label, mode: "ceping",}
};

//获取全链路跳转url地址
const allChainTo = computed(() => {
  const query = getAllChainToQuery();
  const routerQuery = new URLSearchParams();
  keys(query).forEach((key) => {
    routerQuery.append(key, String(query[key]));
  });
  return `/experience-index/position?${routerQuery.toString()}`;
});


const events = reactive({
  allChain: async () => {
    //阻止原生路由跳转事件
    event.preventDefault();
    //修改当前路由
    await $router.push({ name: `experience-index`, query: getAllChainToQuery() });
    //打开一个全屏dialog
    allChainVisible.value = true;
    //刷新全链路
    nextTick(() => {
      console.log("mark-strategy nextTick");
      proxy.$refs.markPositionRef.getTraceinfo();
    });
  },
});
</script>

<style lang="scss" scoped>
.process-box {
  height: 180px;
  width: 220px;
  padding: 8px;
  background: #f6f3f3;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  .label {
    font-size: 12px;
    color: #909399;
    margin-bottom: 4px;
  }

  .process-name {
    font-size: 14px;
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    padding-right: 24px;
    margin-bottom: 8px;
  }

  .edit-params-btn {
    width: 80px;
    margin: 0 auto;
    font-size: 12px;
    padding: 4px 12px;
    transition: all 0.3s ease;
  }

  .clear-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);

    .clear-btn {
      opacity: 1;
    }
  }
}
</style>
