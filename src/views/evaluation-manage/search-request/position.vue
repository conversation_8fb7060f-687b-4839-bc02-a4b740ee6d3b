<template>
  <page-wrapper route-name="search-request::position::">
    <Position :loadDataApi="loadListData"/>
  </page-wrapper>
</template>

<script lang="ts" setup>
import useCtx from "@/hooks/useCtx";
import { ref, reactive, onMounted, nextTick } from "vue";
import Position from "@/views/common/position/index.vue";
import * as evaluationApi from "@/api/search-request";
const { $router, proxy, $app } = useCtx();
const routeQuery = ref($app.$route.query);
//列表查询
const loadListData = () => {

  console.log("routeQuery",routeQuery.value);
  
  const params = $app.$route.query.traceId;
  const region = $app.$route.query.region;
  const searchType = $app.$route.query.searchType;
  return evaluationApi.getSearchTrace(params,region,searchType);
};
</script>
<style lang="scss" scoped>
</style>
