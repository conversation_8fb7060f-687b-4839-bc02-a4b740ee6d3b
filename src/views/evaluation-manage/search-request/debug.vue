<template>
  <page-wrapper route-name="search-request-debug::">
    <div class="search-request-debug-table">
      <table-page
        ref="myTableRef"
        name="search-request-debug-table"
        :columns="columns"
        :query="query"
        :loadDataApi="loadListData"
        :transformQuery="transformQuery"
        :transformListData="transformListData"
        operationAuth="/base/#/search-request-debug/edit"
        :loadImmediately="true"
        @operation="handleOperation"
        :withStoreColumn="true"
      >
        <template #query>
          <my-query ref="queryRef" :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset"></my-query>
          <div class="custom-operation">
            <my-operation>
              <template #buttonGroup>
                <my-button type="primary" @click="events.searchBtn">搜索</my-button>
                <el-popover placement="bottom" :width="200" trigger="hover">
                  <div>
                    <el-input-number v-model="exportNum" placeholder="请输入数量" />
                  </div>
                  <template #reference>
                    <my-button type="export" @click="events.export">导出</my-button>
                  </template>
                </el-popover>
                <my-button type="primary" @click="events.openTaskList">导出列表</my-button>
              </template>
            </my-operation>
          </div>
        </template>
      </table-page>
      <my-drawer class="mock-add" width="900px" v-model="historyVisible" title="导出列表" :showConfirm="false" @close="historyVisible = false">
        <TaskList ref="TaskListRef" :queryDisplay="false" :queryItemsIndex="queryItems"> </TaskList>
      </my-drawer>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { assign } from "lodash";
import useStore from "@/store";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as commonApi from "@/api/common";
import * as searchResquestApi from "@/api/search-request";
import TaskList from "@/views/evaluation-manage/search-request/taskList.vue";
import { getEnableList } from "@/api/app";
import * as util from "@/utils/common";
import moment from "moment";

const { $app, proxy, $auth, $router } = useCtx();
const historyVisible = ref(false);
const { word, common } = useStore();
const exportNum = ref(100);

// 列配置
const columns = ref([
  {
    prop: "pvName",
    label: "请求对象",
    minWidth: 200,
  },
  {
    prop: "flowTypeRender",
    label: "对象类型",
    minWidth: 120,
  },
  {
    prop: "query",
    label: "Query",
    width: 180,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        $router.push({
          name: "search-request-debug::position",
          query: {
            metaLabel: ["全链路定位"],
            traceId: record.traceId,
            region: query.value.region,
            searchType: "debug"
          },
        });
      },
    },
  },
  {
    prop: "from",
    label: "请求来源",
    minWidth: 120,
  },
  {
    prop: "topK",
    label: "topK",
    minWidth: 85,
  },
  {
    prop: "scene",
    label: "场景标签",
    minWidth: 120,
  },
  {
    prop: "intent",
    label: "检索意图",
    width: 120,
  },
  { prop: "docsCount", label: "结果数量", minWidth: 110 },
  { prop: "traceId", label: "traceId", width: 200, withCopy: true },
  { prop: "account", label: "请求人", width: 150 },
  { prop: "createdDateRender", label: "请求时间", width: 170 },
]);

// 查询面板
const query = ref<any>({});

const queryItems = ref<any>({
  region: {
    label: "环境",
    type: "select",
    options: [],
    defaultValue: "",
    attrs: { clearable: false },
  },
  pvid: {
    label: "请求对象",
    type: "select",
    options: [],
    defaultValue: "",
    attrs: { clearable: true },
  },
  flowType: {
    label: "对象类型",
    type: "select",
    options: [
      { label: "场景策略", value: "-1" },
      { label: "产品方案", value: "-2" },
    ],
    defaultValue: "",
    attrs: { clearable: true },
  },
  from: {
    label: "请求来源",
    type: "select",
    options: [],
    defaultValue: "",
    attrs: { clearable: true },
  },
  query: {
    label: "query",
    type: "input",
    defaultValue: "",
    attrs: {},
  },
  traceId: {
    label: "traceId",
    type: "input",
    defaultValue: "",
    attrs: {},
  },
  account: {
    label: "请求人",
    type: "input",
    defaultValue: "",
    attrs: {},
  },
  topK: {
    label: "TopK",
    type: "input",
    defaultValue: "",
    attrs: {},
  },
  scene: {
    label: "场景标签",
    type: "input",
    defaultValue: "",
    attrs: { clearable: true },
  },
  intent: {
    label: "检索意图",
    type: "select",
    options: [],
    defaultValue: [],
    attrs: { clearable: true, multiple: true },
  },
  docsCount: {
    label: "结果数量",
    type: "input",
    defaultValue: "",
    attrs: { clearable: true },
  },
  createdDate: {
    label: "请求时间",
    type: "datetimerange",
    options: [],
    defaultValue: [],
    attrs: { clearable: false },
    events: {
      change: (newVal: any[]) => {
        if (newVal[0]?.endsWith("00:00:00") && newVal[1]?.endsWith("00:00:00")) {
          queryItems.value.createdDate.modelValue = [newVal[0], newVal[1].replace("00:00:00", "23:59:59")];
        }
      },
    },
  },
});

const handleParams = (data: any) => {
  const { createdDate, ...rest } = data;
  if (createdDate[0]?.endsWith("00:00:00") && createdDate[1]?.endsWith("00:00:00")) {
    createdDate[1] = createdDate[1].replace("00:00:00", "23:59:59");
  }
  // 北京时间转UTC
  rest.beginTime = moment(createdDate[0]).utc().format("YYYY-MM-DD HH:mm:ss");
  rest.endTime = moment(createdDate[1]).utc().format("YYYY-MM-DD HH:mm:ss");
  return rest;
};

const getRegionOptions = () => {
  word.getAreaList().then((values: any) => {
    const regionOptions = values.map((item: any) => ({
      ...item,
      label: item.name,
      value: item.code,
    }));

    // 设置环境和请求对象的选项
    queryItems.value.region.options = regionOptions;

    const regionCode = values[0].code;
    query.value.region = regionCode;
    queryItems.value.region = { ...queryItems.value.region, modelValue: regionCode, defaultValue: regionCode };
  });
};

getRegionOptions();

const getSceneVersion = async () => {
  const res = await commonApi.getSceneVersion({});
  queryItems.value.pvid.options = res.data.map((item: any) => ({
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.id,
  }));
};

getSceneVersion();

// 获取请求来源选项
const getFromOptions = () => {
  queryItems.value.from.options = [
    { label: "体验页面", value: "体验页面" },
    { label: "标注页面", value: "标注页面" },
    { label: "画布调试", value: "画布调试" },
  ];
};

getFromOptions();

// 获取检索意图选项
const getIntent = () => {
  searchResquestApi.getSearchIntent().then((res) => {
    queryItems.value.intent.options = res.data.map((item: any) => ({
      ...item,
      value: item.dictCode,
      label: item.dictName,
    }));
  });
};

getIntent();

// 列表查询
const loadListData = async (data: any) => {
  return new Promise((resolve: any) => {
    if (!data.region) {
      return;
    }
    searchResquestApi.getSearchResults(handleParams(data)).then((result) => {
      result.content = result.content.map((item: any, index: number) => ({
        ...item,
        order: (result.pageNum - 1) * result.pageSize + index + 1,
      }));
      resolve(result);
    });
  });
};
// 转换传参
const transformQuery = ({ ...rest }) => {
  const query = {
    searchType: "debug",
    ...rest,
  };
  if (rest.pvid) query.pvidList = [rest.pvid];
  return query;
};
// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = timeC.format(x.createTime, "YYYY-MM-DD HH:mm:ss");
    x.flowTypeRender = queryItems.value.flowType.options.find((item: any) => item.value === x.flowType)?.label;
    return x;
  });
};

// 事件列表
const events = reactive({
  searchBtn: () => {
    proxy.$refs.queryRef.searchImmediatelyFun();
  },
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  openTaskList: () => {
    historyVisible.value = true;
    nextTick(() => {
      proxy.$refs.TaskListRef.loadList();
    });
  },
  export: () => {
    searchResquestApi.exportSearchResults({ ...handleParams(query.value), count: exportNum.value }).then((res) => {
      $app.$message.success(`导出成功`);
    });
  },
});

const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};

// 初始化
onMounted(() => {
  // 设置 createdDate 默认值为当天的开始和结束时间
  const today = new Date();
  const startDate = timeC.format(new Date(today.setHours(0, 0, 0, 0)));
  const endDate = timeC.format(new Date(today.setHours(23, 59, 59, 999)));
  query.value.createdDate = [startDate, endDate];
  queryItems.value.createdDate.defaultValue = [startDate, endDate];
  queryItems.value.createdDate.modelValue = [startDate, endDate];
});

// 接口暴露
defineExpose({
  loadList,
});
</script>

<style lang="scss" scoped>
.search-request-debug-table {
  height: 100%;

  ::v-deep {
    .t-query {
      .el-form-item__label {
        width: 125px;
      }

      .el-select {
        width: 180px !important;

        .el-select__selection {
          flex-wrap: nowrap;
        }
      }

      .el-input {
        width: 180px !important;
      }
    }
  }

  ::v-deep {
    .custom-operation {
      padding: 0 8px 8px;
      float: right;
    }
  }
}
</style>
