<template>
  <page-wrapper route-name="performance-test::details">
    <div class="container">
      <!-- 任务基本信息卡片 -->
      <div class="card">
        <div class="card-header">任务基本信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">任务名称</span>
            <span class="info-value">{{ modelValue.name }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">创建时间</span>
            <span class="info-value">{{ modelValue.createdDateRender }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">任务状态</span>
            <span class="info-value status" :class="currentStatus?.type">{{ currentStatus?.label }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">压测路数</span>
            <span class="info-value">{{ modelValue.concurrency }}</span>
          </div>
          <div class="info-item double-cols">
            <span class="info-label">实例列表</span>
            <span class="info-value">{{ modelValue.instances.join("，") }}</span>
          </div>
        </div>
      </div>

      <!-- 整体性能指标卡片 -->
      <div class="card">
        <div class="card-header">整体性能指标</div>
        <div class="metrics-grid">
          <div class="metric-item">
            <div class="metric-label">90%耗时</div>
            <div class="metric-value metric-90th">{{ modelValue.p90 }}ms</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">QPS</div>
            <div class="metric-value metric-qps">{{ modelValue.qps }}</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">请求成功率</div>
            <div class="metric-value metric-success">{{ modelValue.successRate }}</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">错误数量</div>
            <div class="metric-value metric-error">{{ modelValue.errCount || 0 }}</div>
          </div>
        </div>
      </div>

      <!-- 错误信息详情卡片 -->
      <div class="card" v-if="modelValue.errorDetailList.length > 0">
        <div class="card-header">错误信息详情（共 {{ modelValue.errorDetailList.length }} 条）</div>
        <div id="error-list">
          <div class="error-item" v-for="(item, index) in displayedErrors" :key="item.traceId || index">
            <div class="error-header">
              <span class="error-code">错误代码: {{ item.code }}</span>
              <span class="error-time">{{ item.time }}</span>
            </div>
            <div class="error-details">
              <div>TraceId: {{ item.traceId }}</div>
              <div>AppId: {{ item.appId }}</div>
              <div>错误信息: {{ item.msg }}</div>
            </div>
            <a class="view-details" @click="toggleErrorDetails(index)">查看完整错误详情</a>
            <div class="error-full-details" :id="`error-details-${index}`">
              <p style="word-wrap: break-word"><strong>input:</strong> {{ item.input }}</p>
              <p style="word-wrap: break-word"><strong>output:</strong> {{ item.output }}</p>
            </div>
          </div>
        </div>
        <div v-if="hasMoreErrors" class="load-more-container">
          <button class="load-more-btn" @click="loadMoreErrors">
            加载更多（已显示 {{ displayedErrorCount }} / {{ modelValue.errorDetailList.length }} 条）
          </button>
        </div>
      </div>

      <!-- 子服务性能数据 -->
      <div class="card">
        <div class="card-header">
          子服务性能数据
          <button @click="loadSubServicesChart" :disabled="subServicesObj.loading" class="refresh-btn">
            {{ subServicesObj.loading ? "刷新中..." : "刷新数据" }}
          </button>
        </div>

        <div v-if="subServicesObj.error" class="error-message">
          {{ subServicesObj.error }}
        </div>

        <div v-if="subServicesObj.loading" class="loading-message">正在加载子服务性能数据...</div>

        <div v-else class="chart-container">
          <div ref="subServicesChartRef" style="width: 100%; height: 400px"></div>
        </div>
      </div>

      <!-- QPS趋势 -->
      <div class="card">
        <div class="card-header">
          QPS趋势
          <button @click="loadQpsChart" :disabled="qpsObj.loading" class="refresh-btn">
            {{ qpsObj.loading ? "刷新中..." : "刷新数据" }}
          </button>
        </div>

        <div v-if="qpsObj.error" class="error-message">
          {{ qpsObj.error }}
        </div>

        <div v-if="qpsObj.loading" class="loading-message">正在加载Qps趋势数据...</div>

        <div v-else class="chart-container">
          <div ref="qpsChartRef" style="width: 100%; height: 400px"></div>
        </div>
      </div>

      <!-- 性能趋势 -->
      <div class="card">
        <div class="card-header">
          性能趋势
          <button @click="loadApiCostChart" :disabled="apiCostObj.loading" class="refresh-btn">
            {{ apiCostObj.loading ? "刷新中..." : "刷新数据" }}
          </button>
        </div>

        <div v-if="apiCostObj.error" class="error-message">
          {{ apiCostObj.error }}
        </div>

        <div v-if="apiCostObj.loading" class="loading-message">正在加载性能趋势数据...</div>

        <div v-else class="chart-container">
          <div ref="apiCostChartRef" style="width: 100%; height: 400px"></div>
        </div>
      </div>
    </div>
  </page-wrapper>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from "vue";
import * as evalPerformanceTestApi from "@/api/eval-performance-test";
import usePerformanceStore from "@/store/performance";
import echarts from "@/components/echarts/index";

// 类型定义
interface PrometheusValue {
  metric: Record<string, any>;
  values: [number, string][];
}

interface ChartObj {
  data: PrometheusValue[];
  loading: boolean;
  error: string;
}

interface ErrorDetail {
  code: string;
  time: string;
  traceId: string;
  appId: string;
  msg: string;
  message: string;
  input?: string;
  output?: string;
}

const performance = usePerformanceStore();

// 状态枚举
const statusEnum = [
  { value: 0, label: "已创建", type: "info" },
  { value: 1, label: "运行中", type: "warning" },
  { value: 2, label: "已完成", type: "success" },
  { value: 3, label: "已终止", type: "danger" },
];

const modelValue = reactive({
  name: "",
  createdDateRender: "",
  status: "",
  concurrency: "",
  instances: [],
  p90: "",
  qps: "",
  successRate: "",
  errCount: 0,
  errorDetailList: [] as ErrorDetail[],
});

// 计算属性：获取当前状态信息
const currentStatus = computed(() => statusEnum.find((item) => item.value === modelValue.status));

// 图表实例管理
const subServicesChartRef = ref();
let subServicesChartInstance: any = null;
const subServicesObj = reactive<ChartObj>({
  data: [],
  loading: false,
  error: "",
});

const qpsChartRef = ref();
let qpsChartInstance: any = null;
const qpsObj = reactive<ChartObj>({
  data: [],
  loading: false,
  error: "",
});

const apiCostChartRef = ref();
let apiCostChartInstance: any = null;
const apiCostObj = reactive<ChartObj>({
  data: [],
  loading: false,
  error: "",
});

// 图表实例数组，用于统一管理
const chartInstances = computed(() => [subServicesChartInstance, qpsChartInstance, apiCostChartInstance]);

// 错误信息分页
const ERROR_PAGE_SIZE = 10;
const displayedErrorCount = ref(ERROR_PAGE_SIZE);

// 计算属性：当前显示的错误列表
const displayedErrors = computed(() => modelValue.errorDetailList.slice(0, displayedErrorCount.value));

// 计算属性：是否还有更多错误
const hasMoreErrors = computed(() => displayedErrorCount.value < modelValue.errorDetailList.length);

// 加载更多错误
const loadMoreErrors = () => {
  displayedErrorCount.value = Math.min(displayedErrorCount.value + ERROR_PAGE_SIZE, modelValue.errorDetailList.length);
};

const toggleErrorDetails = (index: number) => {
  const detailsElement = document.getElementById(`error-details-${index}`);
  if (detailsElement) {
    if (detailsElement.style.display === "block") {
      detailsElement.style.display = "none";
    } else {
      detailsElement.style.display = "block";
    }
  }
};

onMounted(async () => {
  const curRecord = performance.getCurrentRecord();

  // 使用 Object.assign 简化属性赋值
  Object.assign(modelValue, {
    name: curRecord.name,
    createdDateRender: curRecord.createdDateRender,
    status: curRecord.status,
    concurrency: curRecord.concurrency,
    p90: curRecord.p90,
    qps: curRecord.qps,
    successRate: curRecord.successRate,
    errCount: curRecord.errCount,
  });

  const performanceResult = await evalPerformanceTestApi.getPerformanceResult(curRecord.id);
  modelValue.instances = performanceResult.data.instances || [];
  modelValue.errorDetailList = performanceResult.data.errDetails || [];

  // 一次性加载所有图表数据
  await loadAllCharts();

  // 监听窗口大小变化
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  // 统一销毁所有图表实例
  [subServicesChartInstance, qpsChartInstance, apiCostChartInstance].forEach((instance) => {
    instance?.dispose();
  });
  window.removeEventListener("resize", handleResize);
});

// 处理窗口大小变化
const handleResize = () => {
  [subServicesChartInstance, qpsChartInstance, apiCostChartInstance].forEach((instance) => {
    instance?.resize();
  });
};

// 一次性获取所有 Prometheus 图表数据(通过后端代理)
const fetchAllPrometheusData = async () => {
  // 设置所有图表为加载状态
  subServicesObj.loading = true;
  qpsObj.loading = true;
  apiCostObj.loading = true;
  
  subServicesObj.error = "";
  qpsObj.error = "";
  apiCostObj.error = "";

  try {
    const curRecord = performance.getCurrentRecord();
    // 使用后端代理接口一次性获取所有图表数据
    const response = await evalPerformanceTestApi.getPrometheusProxyData(curRecord.id);

    if (response?.data) {
      const { subServices, qps, apiCost } = response.data;
      
      // 分别处理三个图表的数据
      if (subServices?.status === "success" && subServices?.data?.result) {
        subServicesObj.data = subServices.data.result;
      } else {
        subServicesObj.error = "获取子服务数据失败";
      }
      
      if (qps?.status === "success" && qps?.data?.result) {
        qpsObj.data = qps.data.result;
      } else {
        qpsObj.error = "获取QPS数据失败";
      }
      
      if (apiCost?.status === "success" && apiCost?.data?.result) {
        apiCostObj.data = apiCost.data.result;
      } else {
        apiCostObj.error = "获取API耗时数据失败";
      }
    } else {
      const errorMsg = "获取数据失败";
      subServicesObj.error = errorMsg;
      qpsObj.error = errorMsg;
      apiCostObj.error = errorMsg;
    }
  } catch (err) {
    console.error(`获取 Prometheus 数据失败:`, err);
    const errorMsg = "网络请求失败";
    subServicesObj.error = errorMsg;
    qpsObj.error = errorMsg;
    apiCostObj.error = errorMsg;
  } finally {
    subServicesObj.loading = false;
    qpsObj.loading = false;
    apiCostObj.loading = false;
  }
};

// 加载所有图表数据
const loadAllCharts = async () => {
  await fetchAllPrometheusData();
  // 数据加载完成后初始化所有图表
  initSubServicesChart();
  initQpsChart();
  initApiCostChart();
};

// 单独刷新子服务图表
const loadSubServicesChart = async () => {
  await loadAllCharts();
};

// 单独刷新QPS图表
const loadQpsChart = async () => {
  await loadAllCharts();
};

// 单独刷新API耗时图表
const loadApiCostChart = async () => {
  await loadAllCharts();
};

// 通用时间格式化函数
const formatTime = (value: number) => {
  const date = new Date(value);
  return `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
};

// 通用图表初始化函数
const initChart = (ref: any, instance: any, updateFn: () => void) => {
  if (instance) {
    instance.dispose();
  }
  if (ref.value) {
    const newInstance = echarts.init(ref.value);
    updateFn.call(null, newInstance);
    return newInstance;
  }
  return null;
};

// 初始化图表
const initSubServicesChart = () => {
  subServicesChartInstance = initChart(subServicesChartRef, subServicesChartInstance, updateSubServicesChart);
};

// 更新图表
const updateSubServicesChart = (instance?: any) => {
  const chartInstance = instance || subServicesChartInstance;
  if (!chartInstance) return;

  const series = subServicesObj.data.map((s) => ({
    name: s.metric.name || "未知服务",
    type: "line",
    smooth: true,
    showSymbol: false,
    data: s.values.map((v: [number, string]) => [v[0] * 1000, parseFloat(v[1])]),
  }));

  const option = {
    tooltip: {
      trigger: "axis",
      formatter: (params: any[]) => {
        let tip = params[0].axisValueLabel;
        params.forEach((item: any) => {
          tip += `<br/>${item.marker}${item.seriesName}: ${item.value[1].toFixed(1)} ms`;
        });
        return tip;
      },
    },
    legend: {
      top: 30,
      data: series.map((s: any) => s.name),
    },
    grid: {
      left: "5%",
      right: "5%",
      bottom: "10%",
      top: "25%",
      containLabel: true,
    },
    xAxis: {
      type: "time",
      axisLabel: { formatter: formatTime },
    },
    yAxis: {
      type: "value",
      name: "耗时 (ms)",
    },
    series,
  };

  chartInstance.setOption(option);
};

// 初始化QPS图表
const initQpsChart = () => {
  qpsChartInstance = initChart(qpsChartRef, qpsChartInstance, updateQpsChart);
};

// 更新QPS图表
const updateQpsChart = (instance?: any) => {
  const chartInstance = instance || qpsChartInstance;
  if (!chartInstance) return;

  const series = qpsObj.data.map((s) => ({
    name: `QPS (appId: ${s.metric.appId || "unknown"})`,
    type: "line",
    smooth: true,
    showSymbol: false,
    lineStyle: { width: 2, color: "#1890ff" },
    itemStyle: { color: "#1890ff" },
    data: s.values.map((v: [number, string]) => [v[0] * 1000, parseFloat(v[1])]),
  }));

  const option = {
    title: { text: "QPS 监控", left: "center" },
    tooltip: {
      trigger: "axis",
      formatter: (params: any[]) => {
        let tip = params[0].axisValueLabel;
        params.forEach((item: any) => {
          tip += `<br/>${item.marker}${item.seriesName}: ${item.value[1].toFixed(2)} req/s`;
        });
        return tip;
      },
    },
    legend: { show: false },
    grid: {
      left: "5%",
      right: "5%",
      bottom: "10%",
      top: "10%",
      containLabel: true,
    },
    xAxis: {
      type: "time",
      axisLabel: { formatter: formatTime },
    },
    yAxis: {
      type: "value",
      name: "QPS (req/s)",
      min: 0,
    },
    series,
  };

  chartInstance.setOption(option);
};

// 初始化API耗时图表
const initApiCostChart = () => {
  apiCostChartInstance = initChart(apiCostChartRef, apiCostChartInstance, updateApiCostChart);
};

// 更新API耗时图表
const updateApiCostChart = (instance?: any) => {
  const chartInstance = instance || apiCostChartInstance;
  if (!chartInstance) return;

  const series = apiCostObj.data.map((s: PrometheusValue) => ({
    name: `平均响应时间 (appId: ${s.metric.appId || "cc501f15"})`,
    type: "line",
    smooth: true,
    showSymbol: false,
    lineStyle: { width: 2, color: "#52c41a" },
    itemStyle: { color: "#52c41a" },
    data: s.values.map((v: [number, string]) => [v[0] * 1000, parseFloat(v[1]) * 1000]), // 转换为毫秒
  }));

  const option = {
    tooltip: {
      trigger: "axis",
      formatter: (params: any[]) => {
        let tip = params[0].axisValueLabel;
        params.forEach((item: any) => {
          tip += `<br/>${item.marker}${item.seriesName}: ${item.value[1].toFixed(2)} ms`;
        });
        return tip;
      },
    },
    legend: { show: false },
    grid: {
      left: "5%",
      right: "5%",
      bottom: "10%",
      top: "10%",
      containLabel: true,
    },
    xAxis: {
      type: "time",
      axisLabel: { formatter: formatTime },
    },
    yAxis: {
      type: "value",
      name: "响应时间 (ms)",
      min: 0,
    },
    series,
  };

  chartInstance.setOption(option);
};

// ISO 时间转 Unix 秒级时间戳
const isoToUnixSec = (iso: string): number => Math.floor(Date.parse(iso) / 1000);
</script>

<style lang="scss" scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
}

body {
  background-color: #f5f7fa;
  color: #333;
  padding: 20px;
  line-height: 1.6;
}

.container {
  padding: 16px;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 20px;
}

.card + .card {
  margin-top: 16px;
}

.card-header {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-item.double-cols {
  grid-column: span 2;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.info-value {
  font-size: 16px;
  font-weight: 500;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  font-size: 14px;
}

.status.info {
  color: $info-color;
  background-color: $info-bg;
}

.status.warning {
  color: $warning-color;
  background-color: $warning-bg;
}

.status.success {
  color: $success-color;
  background-color: $success-bg;
}

.status.danger {
  color: $danger-color;
  background-color: $danger-bg;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.metric-item {
  text-align: center;
  padding: 15px;
  border-radius: 6px;
  background-color: #f9f9f9;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  margin: 10px 0;
}

.metric-label {
  font-size: 14px;
  color: #666;
}

.metric-90th {
  color: #722ed1;
}

.metric-qps {
  color: #1890ff;
}

.metric-success {
  color: #52c41a;
}

.metric-error {
  color: #f5222d;
}

.error-item {
  border: 1px solid #ffe7e7;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #fff2f0;
}

.error-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.error-code {
  color: #f5222d;
  font-weight: 600;
}

.error-time {
  color: #999;
  font-size: 14px;
}

.error-details {
  font-size: 14px;
  margin-bottom: 10px;
}

.view-details {
  color: #1890ff;
  text-decoration: none;
  font-size: 14px;
  cursor: pointer;
}

.view-details:hover {
  text-decoration: underline;
}

.error-full-details {
  display: none;
  margin-top: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  font-family: monospace;
  font-size: 13px;
  white-space: pre-wrap;
}

.services-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.services-table th {
  background-color: #f8f9fa;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
}

.services-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.services-table tr:hover {
  background-color: #f9f9f9;
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 10px;
}

.chart-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 10px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  text-align: center;
}

.refresh-btn {
  float: right;
  padding: 6px 12px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.refresh-btn:hover:not(:disabled) {
  background-color: #40a9ff;
}

.refresh-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

.error-message {
  color: #f5222d;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}

.loading-message {
  text-align: center;
  color: #666;
  padding: 40px;
  font-size: 16px;
}

.chart-container {
  min-height: 400px;
}

.load-more-container {
  text-align: center;
  margin-top: 20px;
  padding: 10px 0;
}

.load-more-btn {
  padding: 10px 24px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.load-more-btn:hover {
  background-color: #40a9ff;
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

.load-more-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

@media (max-width: 768px) {
  .info-grid,
  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .error-header {
    flex-direction: column;
  }

  .charts-container {
    grid-template-columns: 1fr;
  }

  .chart-item {
    height: 250px;
  }
}
</style>
