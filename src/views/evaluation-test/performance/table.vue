<template>
  <div class="performance-table">
    <table-page
      ref="myTableRef"
      name="performance-table"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :operations="operations"
      @operation="handleOperation"
      with-selection
      @selection-change="handleSelectionChange"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation :selectedTotal="selectedIds.length">
            <template #buttonGroup>
              <my-button type="add" @click="events.add">新建任务</my-button>
              <my-button type="danger" :disabled="selectedIds.length === 0" @click="events.batchDelete">删除</my-button>
            </template>
          </my-operation>
        </div>
      </template>

      <!-- 任务状态列 -->
      <template #status="scope">
        <el-popover placement="right" :width="480" :disabled="scope.row.status !== 1" trigger="hover" @show="updateMissionProgress(scope.row.id)">
          <el-row :gutter="10">
            <el-col :span="12" v-if="scope.row.progressTotalRounds > 0">
              <span>完成轮数：{{ scope.row.progressFinishedRounds || 0 }} / {{ scope.row.progressTotalRounds || 0 }}</span>
            </el-col>
            <el-col :span="12" v-if="scope.row.progressTotalMinutes > 0">
              <span>运行时间：{{ scope.row.progressRuntimeMinutes || 0 }} / {{ scope.row.progressTotalMinutes || 0 }} 分钟</span>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <span>开始时间：{{ scope.row.progressStartTime || "-" }}</span>
            </el-col>
            <el-col :span="12">
              <span>结束时间：{{ scope.row.progressEndTime || "-" }}</span>
            </el-col>
          </el-row>
          <template #reference>
            <status-dot
              :type="statusEnum.find((item1) => item1.value == scope.row?.status)?.type"
              :name="statusEnum.find((item1) => item1.value == scope.row?.status)?.label"
            />
          </template>
        </el-popover>
      </template>
    </table-page>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { assign } from "lodash";
import { timeC, dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as evalPerformanceTestApi from "@/api/eval-performance-test";

const { $app } = useCtx();
const emit = defineEmits(["edit", "view-result"]);

// 表格引用
const myTableRef = ref();

// 选中的行ID
const selectedIds = ref<string[]>([]);

// 状态枚举
const statusEnum = [
  { value: 0, label: "已创建", type: "info" },
  { value: 1, label: "运行中", type: "warning" },
  { value: 2, label: "已完成", type: "success" },
  { value: 3, label: "已终止", type: "danger" },
];

// 更新任务进度信息
const updateMissionProgress = (missionId: string) => {
  evalPerformanceTestApi
    .getMissionProgress(missionId)
    .then((res) => {
      if (!res.data) return;
      let data = res.data;

      let tableData = myTableRef.value?.getTableData() || [];
      const list = tableData.map((x: any) => {
        if (x.id == missionId) {
          x.status = data.status;
          x.progressFinishedRounds = data.finishedRounds;
          x.progressTotalRounds = data.totalRounds;
          x.progressRuntimeMinutes = data.runtimeMinutes;
          x.progressTotalMinutes = data.totalMinutes;
          x.progressStartTime = data.beginTime ? timeC.format(data.beginTime, "YYYY-MM-DD HH:mm:ss") : "";
          x.progressEndTime = data.endTime ? timeC.format(data.endTime, "YYYY-MM-DD HH:mm:ss") : "";
        }
        return x;
      });
      myTableRef.value?.setTableData(list);
    })
    .catch((error) => {
      console.error("获取任务进度失败:", error);
    });
};

// 查询条件
const query = ref<any>({});

// 查询项配置
const queryItems = ref<any>({
  name: {
    type: "input",
    label: "任务名称",
    width: "200px",
    modelValue: "",
    attrs: {
      placeholder: "请输入任务名称",
    },
  },
  createTime: {
    type: "daterange",
    label: "创建时间",
    width: "300px",
    modelValue: [],
    attrs: {
      placeholder: "请选择创建时间范围",
    },
  },
  status: {
    label: "任务状态",
    modelValue: "",
    width: "150px",
    type: "select",
    options: [...statusEnum],
    attrs: {
      clearable: true,
    },
  },
});

// 列配置
const columns = ref([
  {
    prop: "name",
    label: "任务名称",
    minWidth: 200,
    fixed: "left",
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        if (record.status === 0) {
          events.edit(record);
          return;
        }
        events.detail(record);
      },
    },
  },
  {
    prop: "createdDateRender",
    label: "创建时间",
    width: 170,
  },
  {
    prop: "status",
    label: "任务状态",
    width: 110,
    slotName: "status",
    showOverflowTooltip: false,
    sortable: false,
  },
  {
    prop: "concurrency",
    label: "压测路数",
    width: 150,
  },
  {
    prop: "p90",
    label: "90%耗时(单位ms)",
    width: 170,
  },
  {
    prop: "qps",
    label: "Qps",
    width: 170,
  },
  {
    prop: "successRate",
    label: "成功率",
    width: 170,
  },
  {
    prop: "errCount",
    label: "错误数量",
    width: 170,
  },
  {
    prop: "operation",
    label: "操作",
    width: 180,
    fixed: "right",
  },
]);

// 操作列配置
const operations = [
  {
    type: "start",
    label: "开始运行",
    exist: (record: any) => record.status === 0,
  },
  {
    type: "viewResult",
    label: "查看结果",
    btnType: "success",
    exist: (record: any) => record.status === 2 || record.status === 3,
  },
  {
    type: "stop",
    label: "停止",
    btnType: "warning",
    disabled: (record: any) => record.status !== 1,
    disabledTips: (record: any) => {
      if (record.status !== 1) {
        return "只有运行中的任务可以停止";
      }
    },
  },
  {
    type: "delete",
    label: "删除",
    btnType: "danger",
    disabled: (record: any) => record.status === 1,
    disabledTips: (record: any) => {
      if (record.status === 1) {
        return "运行中的任务不能删除";
      }
    },
  },
];

// 加载数据
const loadListData = (params: any) => {
  return evalPerformanceTestApi.pagePerformanceMission(params);
};

// 数据转换
const transformListData = (data: any) => {
  // data 是 content 数组，不是整个响应对象
  return data.map((item: any) => ({
    ...item,
    createdDateRender: item.createdDate ? timeC.format(item.createdDate, "YYYY-MM-DD HH:mm:ss") : "-",
    lastModifiedDateRender: item.lastModifiedDate ? timeC.format(item.lastModifiedDate, "YYYY-MM-DD HH:mm:ss") : "-",
  }));
};

// 事件处理
const events = reactive({
  // 搜索
  search: (obj: any) => {
    query.value = {};
    query.value = assign({}, query.value, obj);
    if (obj.createTime && obj.createTime.length === 2) {
      const [start, end] = obj.createTime;
      // 将本地时间转换为 UTC 的 ISO 字符串（Instant）
      query.value.beginDate = start ? new Date(new Date(start).setHours(0, 0, 0, 0)).toISOString() : undefined;
      query.value.endDate = end ? new Date(new Date(end).setHours(23, 59, 59, 999)).toISOString() : undefined;
    }
  },

  // 重置
  reset: (obj: any) => {
    for (let key in obj) {
      queryItems.value[key].modelValue = obj[key].modelValue;
    }
  },

  // 新建任务
  add: () => {
    console.log("新建任务");
    emit("edit", "add", {});
  },

  // 编辑任务
  edit: async (record: any) => {
    console.log("编辑任务", record);
    try {
      emit("edit", "edit", record);
    } catch (error: any) {
      $app.$message.error(error.message || "获取任务详情失败");
    }
  },

  // 查看任务
  detail: (record: any) => {
    console.log("查看任务", record);
    emit("edit", "view", record);
  },

  /**
   * 任务正常执行完时跳转详情页， 异常时弹框展示errMsg
   * @param record
   */
  viewResult: (record: any) => {
    if (record.status === 3) {
      $app.$message.error(record.errMsg);
      return;
    }

    emit("view-result", record);
  },

  // 开始运行
  start: (record: any) => {
    $app.$confirm({ title: `确定要开始运行该任务吗？` }).then(() => {
      evalPerformanceTestApi.startPerformanceMission(record.id).then(() => {
        $app.$message.success("开始运行成功");
        refresh();
      });
    });
  },

  // 停止任务
  stop: (record: any) => {
    $app.$confirm({ title: `确定要停止该任务吗？` }).then(() => {
      evalPerformanceTestApi.stopPerformanceMission(record.id).then(() => {
        $app.$message.success("停止任务成功");
        refresh();
      });
    });
  },

  // 删除单个任务
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.name}?`,
      })
      .then(() => {
        evalPerformanceTestApi.deletePerformanceMission({ ids: [record.id] }).then(() => {
          refresh();
          $app.$message.success(`删除 ${record.name} 成功`);
        });
      });
  },

  // 批量删除
  batchDelete: async () => {
    if (selectedIds.value.length === 0) {
      $app.$message.warning("请选择要删除的任务");
      return;
    }

    try {
      await $app.$messageBox.confirm(`确定要删除选中的 ${selectedIds.value.length} 个任务吗？`, "提示", {
        type: "warning",
      });

      await evalPerformanceTestApi.deletePerformanceMission({ ids: selectedIds.value });
      $app.$message.success("删除成功");
      selectedIds.value = [];
      refresh();
    } catch (error: any) {
      if (error !== "cancel") {
        $app.$message.error(error.message || "删除失败");
      }
    }
  },
});

// 处理操作
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (typeof events[type as keyof typeof events] === "function") {
    (events[type as keyof typeof events] as any)(record);
  }
};

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedIds.value = selection.map((item) => item.id);
};

// 刷新表格
const refresh = () => {
  myTableRef.value?.loadData();
};

// 暴露方法
defineExpose({
  refresh,
});
</script>

<style lang="scss" scoped>
.performance-table {
  height: 100%;
}
</style>
