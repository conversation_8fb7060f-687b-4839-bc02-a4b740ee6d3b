<template>
  <page-wrapper route-name="consistency-test::details">
    <div class="consistency-detail">
      <!-- 任务信息与环境配置 -->
      <el-card class="task-info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">任务信息与环境配置</span>
          </div>
        </template>

        <!-- 基本信息 -->
        <div class="basic-info">
          <h4 class="section-title">基本信息</h4>
          <div class="info-grid">
            <div class="info-item" v-if="missionResult.name">
              <label>任务名称：</label>
              <span>{{ currentmissionBase?.name }}</span>
            </div>
            <div class="info-item">
              <label>任务负责人：</label>
              <span>{{ currentmissionBase?.createdBy || '-' }}</span>
            </div>
            <div class="info-item">
              <label>开始时间：</label>
              <span>{{ missionResult.startTimeRender || '-' }}</span>
            </div>
            <div class="info-item">
              <label>结束时间：</label>
              <span>{{ missionResult.endTimeRender || '-' }}</span>
            </div>
            <div class="info-item">
              <label>测试集：</label>
              <span>{{queryGroupOptions.find((item: any) => item.value === currentmissionBase?.queryGroupId)?.label ||
                '-'
              }}</span>
            </div>
            <div class="info-item highlight">
              <label>排序一致性结果：</label>
              <span class="result-value">{{ missionResult.sortPercentage || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 环境配置对比 -->
        <div class="env-config">
          <h4 class="section-title">环境配置对比</h4>
          <div class="env-list">
            <div class="env-item">
              <div class="env-label">环境A(基准环境)：</div>
              <div class="env-details">
                <span class="env-name">{{ getRegionName(currentmissionBase?.processConfigA.region) }}</span>
                <span class="env-strategy">方案：{{ getProcessName(currentmissionBase?.processConfigA.processId) }}</span>
                <span class="env-app">流程参数：{{ objToCommaString(currentmissionBase?.processConfigA.params) }}</span>
              </div>
            </div>

            <div class="env-item">
              <div class="env-label">环境B：</div>
              <div class="env-details">
                <span class="env-name">{{ getRegionName(currentmissionBase?.processConfigB.region) }}</span>
                <span class="env-strategy">方案：{{ getProcessName(currentmissionBase?.processConfigA.processId) }}</span>
                <span class="env-app">流程参数：{{ objToCommaString(currentmissionBase?.processConfigB.params) }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 一致性结果统计 -->
      <div class="consistency-stats">
        <div class="stats-section">
          <div class="stats-header">
            <h3 class="stats-title">排序一致性</h3>
            <div class="stats-rate">整体一致性：{{ missionResult.sortPercentage || '0.0%' }}</div>
          </div>
          <p class="stats-desc">要求结果列表内容和顺序完全相同才算一致</p>

          <div class="stats-table">
            <div class="table-header">
              <span class="col-type">结果类型</span>
              <span class="col-count">条数</span>
              <span class="col-percent">占比</span>
            </div>
            <div class="table-row" v-for="(item, index) in (missionResult.statisticsList || [])" :key="index">
              <span class="col-type" :style="getResultTypeStyle(item.code)">{{ getNodeNameByCode(item.code) }}不一致</span>
              <span class="col-count">{{ item.inConsisCount4Sort }}</span>
              <span class="col-percent">{{ item.percentage4Sort }}</span>
            </div>
          </div>
        </div>

        <div class="stats-section">
          <div class="stats-header">
            <h3 class="stats-title">内容一致性</h3>
            <div class="stats-rate">整体一致性：{{ missionResult.contentPercentage || '0.0%' }}</div>
          </div>
          <p class="stats-desc">只要结果内容完全相同（不管顺序）就算一致</p>

          <div class="stats-table">
            <div class="table-header">
              <span class="col-type">结果类型</span>
              <span class="col-count">条数</span>
              <span class="col-percent">占比</span>
            </div>
            <div class="table-row" v-for="(item, index) in (missionResult.statisticsList || [])" :key="index">
              <span class="col-type" :style="getResultTypeStyle(item.code)">{{ getNodeNameByCode(item.code) }}不一致</span>
              <span class="col-count">{{ item.inConsisCount4Content }}</span>
              <span class="col-percent">{{ item.percentage4Content }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 不一致doc列表展示 -->
      <el-card class="inconsistent-docs" shadow="never">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <el-tabs v-model="activeTab" @tab-change="handleTabChange">
                <el-tab-pane label="排序不一致" name="sort">
                  <template #label>
                    <span>排序不一致 {{ getCount("sort") }}条</span>
                  </template>
                </el-tab-pane>
                <el-tab-pane label="内容不一致" name="content">
                  <template #label>
                    <span>内容不一致 {{ getCount("content") }}条</span>
                  </template>
                </el-tab-pane>
              </el-tabs>
            </div>
            <div class="header-right">
              <el-button @click="exportInconsistentDocs" :loading="exportLoading">
                导出结果
              </el-button>
              <el-input v-model="searchQuery" placeholder="搜索 Query..." style="width: 200px;" clearable
              @blur="handleSearch">
                <template #prefix>
                  <el-icon>
                    <Search />
                  </el-icon>
                </template>
              </el-input>
            </div>
          </div>
        </template>


        <!-- 统计卡片 -->
        <div class="summary-cards">
          <div
            class="summary-card"
            :class="{ 'selected': searchParams.nodeCode === item.code }"
            v-for="(item, index) in missionResult.statisticsList"
            :key="index"
            @click="handleCardClick(item.code)"
          >
            <div class="card-title">{{ getNodeNameByCode(item.code) }}</div>
            <div class="card-value">{{ getCount(activeTab as "sort" | "content", item.code) }}</div>
            <div class="card-subtitle">条结果不一致</div>
          </div>
        </div>

        <!-- 不一致文档列表 -->
        <div class="docs-list">
          <table-page ref="docsTableRef" name="inconsistent-docs-table" :columns="docsColumns" :query="docsQuery"
            :loadDataApi="loadDocsData" :transformListData="transformDocsData" :defaultPageSizes='[5,10,20,50,100]'
            :withSort="false" :showOverflowTooltip="false"
          >

            <!-- A环境文档展示插槽 -->
            <template #docsASlot="scope">
              <div class="docs-container">
                <div v-if="scope.row.docsA && scope.row.docsA.length > 0" class="docs-list-wrapper">
                  <div
                    v-for="(doc, index) in scope.row.docsA"
                    :key="`docsA-${scope.row.id}-${index}`"
                    class="doc-card doc-card-a clickable-card"
                    @click="handleDocCardClick(doc, 'A')"
                  >
                    <div class="doc-header">
                      <span class="doc-index">{{ index + 1 }}.</span>
                      <span class="doc-title">{{ doc.title || '无标题' }}</span>
                    </div>
                    <div class="doc-content" :class="{ 'expanded': doc._expanded }">
                      <div class="content-text">
                        {{ doc.content || doc.summary || '无内容' }}
                      </div>
                      <div class="content-actions" v-if="isContentLong(doc.content || doc.summary)">
                        <el-button
                          type="text"
                          size="small"
                          @click="toggleContent(doc)"
                          class="toggle-btn"
                        >
                          {{ doc._expanded ? '收起' : '更多' }}
                        </el-button>
                      </div>
                    </div>
                    <div class="doc-meta">
                      <div class="meta-row">
                        <span class="meta-label">ID：</span>
                        <span class="meta-value">{{ doc.id || '-' }}</span>
                      </div>
                      <div class="meta-row">
                        <span class="meta-label">结果来源：</span>
                        <span class="meta-value">{{ doc._indexName || '-' }} {{ doc._indexCode || '-' }}</span>
                      </div>
                      <div class="meta-row">
                        <span class="meta-label">得分（位次）：</span>
                        <span class="meta-value">{{getScoreAndIndex( scope.row.nodeCode, doc)}}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="no-docs">
                  <span>暂无文档</span>
                </div>
              </div>
            </template>

            <!-- B环境文档展示插槽 -->
            <template #docsBSlot="scope">
              <div class="docs-container">
                <div v-if="scope.row.docsB && scope.row.docsB.length > 0" class="docs-list-wrapper">
                  <div
                    v-for="(doc, index) in scope.row.docsB"
                    :key="`docsB-${scope.row.id}-${index}`"
                    class="doc-card doc-card-b clickable-card"
                    @click="handleDocCardClick(doc, 'B')"
                  >
                    <div class="doc-header">
                      <span class="doc-index">{{ index + 1 }}.</span>
                      <span class="doc-title">{{ doc.title || '无标题' }}</span>
                      <span class="doc-status">不一致</span>
                    </div>
                    <div class="doc-content" :class="{ 'expanded': doc._expanded }">
                      <div class="content-text">
                        {{ doc.content || doc.summary || '无内容' }}
                      </div>
                      <div class="content-actions" v-if="isContentLong(doc.content || doc.summary)">
                        <el-button
                          type="text"
                          size="small"
                          @click="toggleContent(doc)"
                          class="toggle-btn"
                        >
                          {{ doc._expanded ? '收起' : '更多' }}
                        </el-button>
                      </div>
                    </div>
                    <div class="doc-meta">
                      <div class="meta-row">
                        <span class="meta-label">ID：</span>
                        <span class="meta-value">{{ doc.id || '-' }}</span>
                      </div>
                      <div class="meta-row">
                        <span class="meta-label">结果来源：</span>
                        <span class="meta-value">{{ doc._indexName || '-' }} {{ doc._indexCode || '-' }}</span>
                      </div>
                      <div class="meta-row">
                        <span class="meta-label">得分（位次）：</span>
                        <span class="meta-value">{{getScoreAndIndex( scope.row.nodeCode, doc)}}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="no-docs">
                  <span>暂无文档</span>
                </div>
              </div>
            </template>

          </table-page>
        </div>
      </el-card>
    </div>
  </page-wrapper>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { Search, SoldOut } from '@element-plus/icons-vue'
import { timeC } from 'turing-plugin'
import useCtx from '@/hooks/useCtx'
import useConsistencyStore from '@/store/consistency'
// import PositionCard from '@/views/common/position/Card.vue' // 不再使用
const { $router, $app } = useCtx();
const consistency = useConsistencyStore();
import * as evalTestApi from '@/api/eval-consistency-test'
import * as querySetApi from '@/api/eval-query-set'
import * as evalSetting from '@/api/eval-setting'
const route = useRoute()
import * as util from "@/utils/common";
import * as commonApi from "@/api/common";

let metaLabel = $router.currentRoute.value.query.metaLabel;
if (!(metaLabel instanceof Array)) {
  metaLabel = [metaLabel];
}

// 场景策略列表
const sceneList = ref<any[]>([]);
const getSceneList = async () => {
  const res = await commonApi.getSceneVersion({});
  sceneList.value = res.data;
};

const getProcessName = (processId: string) => {
  let item = sceneList.value.find((item: any) => item.processId === processId);
  if(!item){
    return processId;
  }
  return `${item.name}(v${util.padNumberToDigits(item.version, 3)})`;
}

const regionOptions = ref<any[]>([])
// 加载环境选项
const loadRegionOptions = async () => {
  try {
    const res = await commonApi.getAreaEnum()
    regionOptions.value = (res.content || []).map((item: any) => ({
      label: item.name,
      value: item.code
    }))
  } catch (error: any) {
    console.error('加载环境选项失败:', error)
  }
}

const getRegionName = (regionCode: string) => {
  return regionOptions.value.find((item: any) => item.value === regionCode)?.label || regionCode
}



//query集
const queryGroupOptions = ref<any[]>([])
// 加载query集选项
const loadQueryGroupOptions = async () => {
  try {
    querySetApi.publishList().then((res) => {
      queryGroupOptions.value = res.data.map((item: any) => ({
        value: item.id,
        label: item.name,
      }));
    });
  } catch (error: any) {
    console.error('加载query集失败:', error)
  }
}
loadQueryGroupOptions();

//关注节点列表
const concernNodeList = ref<any[]>([]);
// 加载关注节点列表
const loadConcernNodeList = async () => {
  evalSetting.listConcernNode().then((res) => {
    concernNodeList.value = res.data;
  })
}
loadConcernNodeList();

const getNodeNameByCode = (code: string) => {
  if (code === "FINAL_RECALL") {
    return "最终结果";
  }
  return concernNodeList.value.find((node: any) => node.code === code)?.name || "-";
}

// 颜色配置 - 为结果类型分配一致的颜色
const resultTypeColors = [
  '#f5222d', '#fa8c16', '#faad14', '#1890ff', '#722ed1',
  '#13c2c2', '#eb2f96', '#52c41a', '#a0d911', '#2f54eb'
]

// 获取结果类型的颜色
const getResultTypeColor = (code: string) => {
  // 使用code的hash值来确保相同的code总是得到相同的颜色
  let hash = 0
  for (let i = 0; i < code.length; i++) {
    hash = ((hash << 5) - hash + code.charCodeAt(i)) & 0xffffffff
  }
  const index = Math.abs(hash) % resultTypeColors.length
  return resultTypeColors[index]
}

// 获取结果类型的样式对象
const getResultTypeStyle = (code: string) => {
  const color = getResultTypeColor(code)
  return {
    color: color,
    fontWeight: 'bold'
  }
}

/**
 * 获取得分、位次
 * @param code 节点code
 */
const getScoreAndIndex = (code: string, doc: any) => {

  let node = concernNodeList.value.find((node: any) => node.code === code);
  if(node){
    return doc[node.scoreField] + " (" + doc[node.indexField] + ")";
  }

}

/**
 * 获取节点的不一致doc总数
 * @param type sort | content
 * @param 节点code 如果不传则获取所有节点的总数  
 */
const getCount = (type: "sort" | "content", code?: string) => {
  // 安全检查：确保statisticsList存在且为数组
  if (!missionResult.value.statisticsList || !Array.isArray(missionResult.value.statisticsList)) {
    return 0;
  }

  let statisticsList = missionResult.value.statisticsList;
  if (code) {
    statisticsList = statisticsList.filter((item: any) => item.code === code);
  }

  if (type === "sort") {
    return statisticsList.reduce((acc: number, cur: any) => acc + (cur.inConsisCount4Sort || 0), 0);
  }

  if (type === "content") {
    return statisticsList.reduce((acc: number, cur: any) => acc + (cur.inConsisCount4Content || 0), 0);
  }

  return 0;
}

// 任务信息
const missionResult = ref<any>({})
const exportLoading = ref(false)

// 从store中获取当前记录数据 记录当前任务基本信息
const currentmissionBase = computed(() => consistency.getCurrentRecord())

// 不一致文档相关
const activeTab = ref<string>('sort')
const searchQuery = ref('')
const searchParams = ref({
  missionId:"",
  query:"",
  nodeCode: "",
  type: 1,
})
const docsTableRef = ref()
const docsQuery = ref<any>({})

// 文档列配置
const docsColumns = ref([
  {
    prop: 'nodeName',
    label: '环节',
    minWidth: 100,
    fixed: 'left',
    sortable: false,
  },
  {
    prop: 'query',
    label: 'QUERY',
    width: 120,
    sortable: true,
  },
  {
    prop: 'docsA',
    label: 'A环境一致性评测详情',
    slotName: 'docsASlot',
    type: "slot",
    'show-overflow-tooltip': false,
    minWidth: 300
  },
  {
    prop: 'docsB',
    label: 'B环境一致性评测详情',
    slotName: 'docsBSlot',
    type: "slot",
    'show-overflow-tooltip': false,
    minWidth: 300,
  }
])

const handleCardClick = (code: string) => {
  console.log("handleCardClick:", code);
  // 如果点击的是已选中的卡片，则取消选中
  if (searchParams.value.nodeCode === code) {
    searchParams.value.nodeCode = "";
  } else {
    searchParams.value.nodeCode = code;
  }
}

watch(searchParams, (newVal) => {
  docsTableRef.value?.loadData();
}, { deep: true })


// 获取任务ID
const missionId = computed(() => route.query.id as string)

// 加载任务信息
const loadTaskInfo = async () => {
  try {
    // 优先使用store中的数据
    const recordData = currentmissionBase.value

    if (missionId.value) {
      //2.获取一致性比对结果
      const res = await evalTestApi.getConsistencyResult(missionId.value)

      console.log("一致性比对结果:", res.data);

      missionResult.value = {
        ...res.data,
        // 如果store中有数据，优先使用store中的数据来补充显示信息
        ...(recordData && {
          name: recordData.name,
          description: recordData.description,
          createUser: recordData.createdBy || recordData.createUser,
          sortPercentage: recordData.sortPercentage,
          status: recordData.status,
          progress: recordData.progress
        }),
        startTimeRender: res.data.beginTime ?
          timeC.format(res.data.beginTime, 'YYYY-MM-DD HH:mm:ss') : '-',
        endTimeRender: res.data.endTime ?
          timeC.format(res.data.endTime, 'YYYY-MM-DD HH:mm:ss') : '-'
      }
    } else {
      //不应该有missionId不存在情况 此处报错处理
      $app.$message.error('任务ID不存在')
    }
  } catch (error: any) {
    $app.$message.error(error.message || '获取任务信息失败')
  }
}

// 加载不一致文档数据
const loadDocsData = (params: any) => {
  searchParams.value.missionId = missionId.value;
  return evalTestApi.pageVsResult(searchParams.value, params);
}

// 文档数据转换
const transformDocsData = (data: any) => {
  console.log("transformDocsData 原始数据:", data);

  const transformedData = data.map((item: any) => {
    // 为docsA和docsB中的每个文档添加展开状态
    const processedDocsA = (item.docsA || item.envADocs || item.envAResults || item.aEnvDocs || []).map((doc: any) => ({
      ...doc,
      _expanded: false // 默认收起状态
    }));

    const processedDocsB = (item.docsB || item.envBDocs || item.envBResults || item.bEnvDocs || []).map((doc: any) => ({
      ...doc,
      _expanded: false // 默认收起状态
    }));

    return {
      ...item,
      // 基础字段
      id: item.id || `${item.missionId}-${item.queryText}-${Date.now()}`,
      nodeName: item.nodeName || item.node || '未知环节',
      query: item.queryText || item.query || '-',
      envAResult: item.envAResult || '-',
      envBResult: item.envBResult || '-',

      // 处理后的文档数据
      docsA: processedDocsA,
      docsB: processedDocsB
    };
  });

  console.log("transformDocsData 转换后数据:", transformedData);
  return transformedData;
}

// 将对象扁平为 "key: val; key2: val2" 的字符串
function objToCommaString(obj: Record<string, any>): string {
  if (!obj || typeof obj !== "object") return "";
  return Object.keys(obj)
    .map((k) => {
      const v = obj[k];
      // 处理数组：去掉[]和内部的引号，用逗号分隔
      if (Array.isArray(v)) {
        const arrayStr = v.map(item => String(item)).join(', ');
        return `${k}: ${arrayStr}`;
      }
      // 处理对象：递归处理
      if (v && typeof v === "object") {
        return `${k}: ${objToCommaString(v)}`;
      }
      // 处理基本类型
      return `${k}: ${String(v)}`;
    })
    .join(';  ');
}

// 处理标签页切换
const handleTabChange = (tabName: any) => {
  activeTab.value = tabName as string
  searchParams.value.type = activeTab.value === "sort" ? 1 : 2;
  searchParams.value.nodeCode = "";
}

// 处理搜索
const handleSearch = () => {
  searchParams.value.query = searchQuery.value;
}

// 导出不一致文档
const exportInconsistentDocs = async () => {
  try {
    exportLoading.value = true
    searchParams.value.missionId = missionId.value;
    await evalTestApi.exportVsResult(searchParams.value).then((res) =>
          util.downloadFile(
            res,
            `一致性对比异常结果_${currentmissionBase.value.name}_${missionId.value}.xlsx`
          )
        );
    $app.$message.success('导出成功')
  } catch (error: any) {
    $app.$message.error(error.message || '导出失败')
  } finally {
    exportLoading.value = false
  }
}


// 格式化文档日期
const formatDocDate = (timestamp: any) => {
  if (!timestamp) return '-'
  try {
    const date = new Date(timestamp)
    return date.toISOString().split('T')[0] // 返回 YYYY-MM-DD 格式
  } catch (error) {
    return '-'
  }
}

// 格式化分数
const formatScore = (score: any) => {
  if (score === null || score === undefined) return '-'
  if (typeof score === 'number') {
    return score.toFixed(6)
  }
  return score.toString()
}

// 判断内容是否过长
const isContentLong = (content: string) => {
  if (!content) return false;

  // 降低阈值：如果内容超过100个字符或包含2个以上的换行符，认为是长内容
  return content.length > 100 || (content.match(/\n/g) || []).length > 1;
}

// 切换内容展开/收起状态
const toggleContent = (doc: any) => {
  // 使用Vue的响应式特性，直接修改对象属性
  doc._expanded = !doc._expanded
}

// 处理文档卡片点击事件
const handleDocCardClick = (doc: any, env: 'A' | 'B') => {
}

// 返回列表
const goBack = () => {
  // 清除store中的数据
  consistency.clearCurrentRecord()
  $app.$router.push({ name: 'consistency-test' })
}

// 组件挂载时加载数据
onMounted(() => {

  getSceneList();
  loadRegionOptions();

  // 如果没有任务ID，尝试从store中获取
  if (!missionId.value && currentmissionBase.value?.id) {
    // 更新路由参数，但不跳转
    $router.replace({
      name: 'consistency-test::detail',
      query: {
        id: currentmissionBase.value.id,
        metaLabel: [currentmissionBase.value.name || '任务详情'],
      }
    })
  }
  loadTaskInfo();
})
</script>

<style lang="scss" scoped>
.consistency-detail {
  height: 100%;
  overflow-y: auto;
  padding: 14px;

  .task-info-card {
    margin-bottom: 16px;
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #409eff;
      }
    }

    .basic-info {
      .section-title {
        margin: 0 0 16px 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
      }

      .info-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .info-item {
          display: flex;
          align-items: center;
          flex: 0 0 auto;
          white-space: nowrap;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
            min-width: 100px;
          }

          span {
            color: #303133;
          }

          &.highlight {
            .result-value {
              color: #67c23a;
              font-weight: 600;
              font-size: 16px;
            }
          }
        }
      }
    }

    .env-config {
      margin-top: 24px;

      .section-title {
        margin: 0 0 16px 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
      }

      .env-list {
        .env-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          padding: 12px;
          background: #f5f7fa;
          border-radius: 4px;

          &:last-child {
            margin-bottom: 0;
          }

          .env-label {
            font-weight: 500;
            color: #606266;
            min-width: 120px;
          }

          .env-details {
            display: flex;
            gap: 24px;
            flex-wrap: wrap;

            span {
              color: #303133;
              font-size: 14px;

              &.env-name {
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }

  .consistency-stats {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    .stats-section {
      flex: 1;
      background: white;
      border-radius: 4px;
      border: 1px solid #ebeef5;
      padding: 20px;

      .stats-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .stats-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #409eff;
        }

        .stats-rate {
          font-size: 14px;
          font-weight: 600;
          color: #67c23a;
        }
      }

      .stats-desc {
        margin: 0 0 16px 0;
        font-size: 12px;
        color: #909399;
      }

      .stats-table {
        .table-header {
          display: flex;
          padding: 8px 0;
          border-bottom: 1px solid #ebeef5;
          font-weight: 600;
          color: #606266;
          font-size: 12px;

          .col-type {
            flex: 2;
          }

          .col-count {
            flex: 1;
            text-align: center;
          }

          .col-percent {
            flex: 1;
            text-align: center;
          }
        }

        .table-row {
          display: flex;
          padding: 8px 0;
          border-bottom: 1px solid #f5f7fa;
          font-size: 12px;

          &:last-child {
            border-bottom: none;
          }

          .col-type {
            flex: 2;
            display: flex;
            align-items: center;

            &.red-tag {
              color: #f56c6c;
            }

            &.orange-tag {
              color: #e6a23c;
            }

            &.yellow-tag {
              color: #f7ba2a;
            }

            &.blue-tag {
              color: #409eff;
            }
          }

          .col-count {
            flex: 1;
            text-align: center;
            color: #303133;
          }

          .col-percent {
            flex: 1;
            text-align: center;
            color: #606266;
          }
        }
      }
    }
  }

  .inconsistent-docs {

    :deep(.el-card__body) {
      padding: 0;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        :deep(.el-tabs__header) {
          margin: 0;
        }

        :deep(.el-tabs__nav-wrap) {
          &::after {
            display: none;
          }
        }

        :deep(.el-tabs__item) {
          padding: 0 20px;
          height: 32px;
          line-height: 32px;
          font-size: 14px;
        }
      }

      .header-right {
        display: flex;
        gap: 12px;
        align-items: center;
      }
    }

    .summary-cards {
      display: flex;
      gap: 16px;
      padding: 20px;
      margin-bottom: 24px;

      .summary-card {
        flex: 1;
        padding: 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
          background: linear-gradient(135deg, #ffffff 0%, #f0f2f5 100%);
          border-color: #409eff;
        }

        &:active {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        // 选中状态样式
        &.selected {
          background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
          border: 2px solid #1890ff;
          box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
          transform: translateY(-2px);

          .card-title {
            color: #1890ff;
            font-weight: 600;
          }

          .card-value {
            color: #0050b3;
            transform: scale(1.05);
          }

          .card-subtitle {
            color: #1890ff;
          }

          &:hover {
            background: linear-gradient(135deg, #d6f3ff 0%, #91d5ff 100%);
            border-color: #40a9ff;
            transform: translateY(-4px);
            box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
          }
        }

        .card-title {
          font-size: 14px;
          color: #606266;
          margin-bottom: 12px;
          font-weight: 500;
          transition: all 0.3s ease;
        }

        .card-value {
          font-size: 28px;
          font-weight: 700;
          margin-bottom: 8px;
          color: #409eff;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
        }

        .card-subtitle {
          font-size: 12px;
          color: #909399;
          transition: all 0.3s ease;
        }
      }
    }

    .docs-list {
      height: 1000px;
      .query-text {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .table-page-wrapper {
        padding: 12px 0;
      }
    }
  }
}

// 文档展示相关样式
.docs-container {
  padding: 8px;
  max-height: 400px;
  overflow-y: auto;

  .docs-list-wrapper {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .no-docs {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
    color: #999;
    font-size: 14px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }
}

// 新的文档卡片样式
.doc-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background-color: #fff;
  transition: all 0.3s ease;

  // 限制卡片最大宽度，防止内容撑开表格
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;

  // 可点击卡片样式
  &.clickable-card {
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  &.doc-card-a {
    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
    border-left: 4px solid #1890ff;
    border-color: #1890ff;

    .doc-index {
      color: #1890ff;
      font-weight: bold;
    }

    &.clickable-card:hover {
      background: linear-gradient(135deg, #d6f3ff 0%, #e6f7ff 100%);
      border-color: #40a9ff;
    }
  }

  &.doc-card-b {
    background: linear-gradient(135deg, #fff2f0 0%, #fef2f2 100%);
    border-left: 4px solid #ff4d4f;
    border-color: #ff4d4f;

    .doc-index {
      color: #ff4d4f;
      font-weight: bold;
    }

    &.clickable-card:hover {
      background: linear-gradient(135deg, #ffe7e6 0%, #fff2f0 100%);
      border-color: #ff7875;
    }

    .doc-status {
      background-color: #fbe0e0;
      color: rgb(209, 10, 10);
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      margin-left: auto;
    }
  }

  .doc-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 8px;

    .doc-index {
      font-size: 14px;
      font-weight: bold;
      min-width: 20px;
    }

    .doc-title {
      font-size: 14px;
      font-weight: 500;
      color: #1f2937;
      flex: 1;
      line-height: 1.4;

      // 限制标题长度，超长省略
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: calc(100% - 100px); // 为序号和状态标签预留空间
    }
  }

  .doc-content {
    font-size: 13px;
    color: #4b5563;
    line-height: 1.5;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;

    .content-text {
      // 默认状态：限制内容显示，超长省略
      max-height: 60px; // 约3行文字的高度
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 3; // 最多显示3行
      line-clamp: 3; // 标准属性
      -webkit-box-orient: vertical;
      word-break: break-word;
      text-overflow: ellipsis;

      // 确保卡片宽度不会因为长内容而撑开
      max-width: 100%;
      white-space: normal;

      // 平滑过渡效果
      transition: max-height 0.3s ease-in-out;
    }

    // 展开状态
    &.expanded .content-text {
      max-height: none;
      display: block;
      -webkit-line-clamp: unset;
      line-clamp: unset;
      -webkit-box-orient: unset;
    }

    .content-actions {
      margin-top: 8px;
      text-align: right;

      .toggle-btn {
        font-size: 12px;
        color: #409eff;
        padding: 0;
        height: auto;

        &:hover {
          color: #66b1ff;
        }
      }
    }
  }

  .doc-meta {
    font-size: 12px;
    color: #6b7280;

    .meta-row {
      display: flex;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .meta-label {
        min-width: 60px;
        font-weight: 500;
      }

      .meta-value {
        flex: 1;
        word-break: break-all;

        // 限制元数据值的显示长度
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 400px; // 限制最大宽度
      }
    }
  }
}

// 表格行高度调整
:deep(.el-table .el-table__row) {
  .docs-container {
    min-height: 100px;
  }
}
</style>
