<template>
  <div class="meta-dict-table" v-show="treeNode?.id">
    <el-card class="info-card">
      <table-page
        ref="myWordTableRef"
        :columns="columns"
        :query="query"
        :loadDataApi="loadListData"
        :transformListData="transformListData"
        :loadImmediately="false"
        :operationAuth="operationAuth"
        :operations="operations"
        @operation="handleOperation"
      >
        <template #query>
          <div class="flexBetweenStart">
            <my-query
              ref="queryRef"
              :queryItems="queryItems"
              :refresh-btn="{ show: false }"
              @search="events.search"
              @reset="events.reset"
            />
            <my-operation>
              <template #buttonGroup>
                <my-button type="add" @click="events.add" operationAuth="/base/#/cloudfunc/edit">新建</my-button>
              </template>
            </my-operation>
          </div>
        </template>
        <template #header>
          <div class="header">
            <span>{{ treeNode?.name }}</span>
          </div>
        </template>
      </table-page>
    </el-card>
    <AddDialog
      ref="addRef"
      @reload="loadList"
      :type="treeNode?.id"
      :treeData="treeData"
      :region="query.region"
      :prodCodeEnum="prodCodeEnum"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from "vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as cloudfuncApi from "@/api/cloudfunc";
import useStore from "@/store";
import { assign, pick, keys } from "lodash";
import * as util from "@/utils/common";
import * as commonApi from "@/api/common";
import { useI18n } from "vue-i18n";
import AddDialog from "./addTable.vue";
const { $app, proxy, $router } = useCtx();
const { word } = useStore();
const { t } = useI18n();
const props = defineProps({
  treeNode: { type: Object, default: {} },
  treeData: { type: Array },
  operationAuth: { type: String },
});
const areaList = ref([]);
const prodCodeEnum = ref([])
const routeQuery =computed(()=> $app.$route.query);
//列配置
const columns = computed(()=>{
  if(props.treeNode?.id === "tag"){
    return [
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 文本可编辑
  {
    prop: "name",
    label: "名称",
    minWidth: 200,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        $router.push({
          name: `cloudfunc::version`,
          query: {
            name: record.name,
            scriptId: record.id,
            type: props.treeNode.id,
            metaLabel: [record.name],
            region: query.value.region,
            regionName:queryItems.value.region.options.find((item:any)=>item.value===query.value.region)?.label,
            envType:queryItems.value.region.options.find((item:any)=>item.value===query.value.region)?.envType,
            code: record.code,
            desc: record.desc,
            createdBy: record.createdBy,
            createdDate: record.createdDateRender,
            typeRender:record.typeRender,
            prodCodeRender:record.prodCodeRender,
          },
        });
      },
    },
  },
  // 文本可复制

  {
    prop: "code",
    label: "编码",
    minWidth: 150,
    withCopy: true,
  },
  {
    prop: "enabled",
    label: "是否启用",
    width: 150,
    custom: "switch",
    customRender: {
      attrs: {
        "active-value": true,
        "inactive-value": false,
      },
      beforeChange: (record: any) => {
        $app
          .$confirm({
            title: `您确认要${record.enabled ? "停用" : "启用"}云函数“${
              record.name
            }”吗？`,
          })
          .then((res) => {
            cloudfuncApi
              .updateMetaFunc(query.value.region, {
                ...record,
                enabled: !record.enabled,
              })
              .then((res) => {
                loadList();
                $app.$message.success(
                  record.enabled ? "云函数停用成功" : "云函数启用成功"
                );
              });
          });
      },
    },
  },
  {
    prop: "desc",
    label: "描述",
    minWidth: 190,
  },
  {
    prop: "typeRender",
    label: "业务类型",
    minWidth: 190,
  },
  {
    prop: "prodCodeRender",
    label: "关联流程",
    minWidth: 190,
  },

  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  { prop: "operation", label: "操作", width: 110, fixed: "right" },
  ]
  }else{
    return [
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 文本可编辑
  {
    prop: "name",
    label: "名称",
    minWidth: 200,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        $router.push({
          name: `cloudfunc::version`,
          query: {
            name: record.name,
            scriptId: record.id,
            type: props.treeNode.id,
            metaLabel: [record.name],
            region: query.value.region,
            regionName:queryItems.value.region.options.find((item:any)=>item.value===query.value.region)?.label,
            envType:queryItems.value.region.options.find((item:any)=>item.value===query.value.region)?.envType,
            code: record.code,
            desc: record.desc,
            createdBy: record.createdBy,
            createdDate: record.createdDateRender,
            typeRender:record.typeRender,
            prodCodeRender:record.prodCodeRender,
          },
        });
      },
    },
  },
  // 文本可复制

  {
    prop: "code",
    label: "编码",
    minWidth: 150,
    withCopy: true,
  },
  {
    prop: "enabled",
    label: "是否启用",
    width: 150,
    custom: "switch",
    customRender: {
      attrs: {
        "active-value": true,
        "inactive-value": false,
      },
      beforeChange: (record: any) => {
        $app
          .$confirm({
            title: `您确认要${record.enabled ? "停用" : "启用"}云函数“${
              record.name
            }”吗？`,
          })
          .then((res) => {
            cloudfuncApi
              .updateMetaFunc(query.value.region, {
                ...record,
                enabled: !record.enabled,
              })
              .then((res) => {
                loadList();
                $app.$message.success(
                  record.enabled ? "云函数停用成功" : "云函数启用成功"
                );
              });
          });
      },
    },
  },
  {
    prop: "desc",
    label: "描述",
    minWidth: 190,
  },
  {
    prop: "typeRender",
    label: "业务类型",
    minWidth: 190,
  },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  { prop: "operation", label: "操作", width: 110, fixed: "right" },
  ]
  }
});

const operations = [
  { type: "edit", label: t("btn.edit") },
  {
    type: "delete",
    label: t("btn.delete"),
    btnType: "danger",
    disabled: (record: any) => record.enabled,
    disabledTips: "已经启用，不能删除",
  },
];
//事件列表
const events = reactive({
  edit: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  add: (type: string, parentId: any, level: number, data?: any) => {
    const params = { ...data, level, parentId };
    proxy.$refs.addRef?.openDialog("add", params);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除"${record.name}"这条数据吗？`,
      })
      .then(() => {
        cloudfuncApi.deleteMetaFunc(query.value.region, record.id).then(() => {
          loadList();
        });
      });
  },
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
});
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
const query = ref<any>({});
const value1 = ref(false);
const queryItems = ref<any>({
  region: {
    type: "select",
    label: "所属区域",
    width: "220px",
    modelValue: "",
    options: [],
    attrs: {
      placeholder: "所属区域",
      clearable: false,
    },
  },
});

async function getAreaOptions() {
  const res: any = await word.getAreaList();
  queryItems.value.region.options = res.map((item) => ({
    ...item,
    label: item.name,
    value: item.code,

  }));

  areaList.value = res;
  const regionCode = routeQuery.value.region || res?.[0]?.code;
  query.value.region = regionCode;
  queryItems.value.region = { ...queryItems.value.region, modelValue: regionCode, defaultValue: regionCode };
}
getAreaOptions();

const updateProdList = async () => {
  const [res1, res2] = await Promise.all([commonApi.getProductAll({}), commonApi.getSceneAll({})]);

  prodCodeEnum.value = [];
  prodCodeEnum.value.push(...res1.data.map((item) => ({ value: item.code, label: `${item.name}【产品方案】` })));
  prodCodeEnum.value.push(...res2.data.map((item) => ({ value: item.code, label: `${item.name}【场景策略】` })));
}
updateProdList()
const handleParams = (params) => {
  return params;
};
//列表查询
const loadListData = (data: any) => {
  return new Promise((resolve: any) => {
    let params: any = {
      ...data,
      'type.equals': props.treeNode.id,
    };
    if (!props.treeNode.id || !query.value.region) {
      return;
    }
    cloudfuncApi
      .getMetaFuncList(query.value.region, handleParams(params))
      .then((result) => {
        resolve(result);
      });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDateRender = timeC.format(
      x.lastModifiedDate,
      "YYYY-MM-DD hh:mm:ss"
    );
    x.prodCodeRender = prodCodeEnum.value.find((item:any)=>item.value===x.prodCode)?.label
    x.typeRender = props.treeData.find((item:any)=>item.id===x.type)?.name
    return x;
  });
};

const loadList = () => {
  proxy.$refs.myWordTableRef?.loadData();
};
//事件声明
const emit = defineEmits(["edit-data", "updateTree"]);
//接口暴露
defineExpose({
  loadList,
});
// watch监听
watch(
  () => props.treeNode,
  (val: any) => {
    if (!val) {
      return;
    }
    value1.value = val.enabled;
    loadList();
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style lang="scss" scoped>
.meta-dict-table {
  height: 100%;
  .header {
    span {
      margin-right: 20px;
    }
    span:first-child {
      font-weight: 550;
    }
    ::v-deep {
      .el-switch {
        height: 0px;
        margin-left: 10px;
      }
    }
  }
}
::v-deep {
  .t-query {
    width: 80%;
    flex: 1;
  }
}
</style>
