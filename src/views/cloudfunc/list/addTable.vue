<template>
  <my-drawer class="offline-dataset-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" @submit="submit" label-width="120px"> </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch, watchEffect } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as cloudfuncApi from "@/api/cloudfunc";
import { NAME_RULE, CODE_RULE } from "@/utils/validate";
import * as commonApi from "@/api/common";
import useValidate from "@/hooks/validate";
// Props
const props = defineProps({
  type: { type: String },
  region: { type: String },
  treeData: { type: Array, default: [] },
  prodCodeEnum: { type: Array, default: [] },
});
const query = ref<any>({ search: "" });
const { $app, proxy, $router } = useCtx();
const { api } = useStore();
// Dialog相关
const isUpdate = computed(() => formType.value === "edit");
const dialogTitle = computed(() => (isUpdate.value ? "编辑云函数" : "新增云函数"));
const dialogVisible = ref(false);
// 默认表单
const defaultForm = reactive({
  id:'',
  name: "",
  code: "",
  desc: "",
  enabled: true,
  type:'',
  prodCode:''
});
// 表单相关
const formType = ref("add");
const formRef = ref(null);
const ruleForm = ref(assign({}, defaultForm));
const routeQuery =computed(()=> $app.$route.query);
const { validateCodeRule } = useValidate();
// 表单规则
const rules = computed(() => ({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  code: [{ required: true, trigger: "blur", message: "编码不能为空", validator: (rule: any, value: any, callback: any) => validateCodeRule(rule, value, callback, "编码不能为空")  }],
  prodCode: [{ required: true, trigger: "blur", message: "关联流程不能为空" }],
}));

const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit();
    }
  });
};
// 表单项
const formItems = ref({
  name: {
    label: "名称",
    type: "input",
    attrs: { maxlength: 100, placeholder: NAME_RULE },
  },
  code: {
    label: "code",
    type: "input",
    disabled:()=>isUpdate.value,
    attrs: { maxlength: 100, placeholder: CODE_RULE },
  },
  desc: {
    label: "描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
  enabled: {
    label: "是否启用",
    type: "switch",
  },
  type: {
    label: "类型",
    type: "select",
    attrs: {
      disabled:true,
      placeholder: "请选择",
      options: props.treeData.map((item: any) => ({value:item.id,label:item.name}))// 数据源 ,
    }
  },
  prodCode:{
    label: "关联流程",
    type: "select",
    options:computed(()=>props.prodCodeEnum),
    hidden:()=>ruleForm.value.type!=='tag',
    disabled:()=>isUpdate.value,
    attrs: {
      placeholder: "请选择",
    }
  }
});
const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    modelValue: "",
    attrs: {
      placeholder: "类别 或 名称 或 字段",
    },
  },
});


// 方法：打开窗口
const openDialog = async (type: string, row: any) => {
  //清空table查询条件
  queryItems.value.search.modelValue = "";
  query.value.search = "";
  //设置表单类型，渲染表单
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    if (!isUpdate.value) {
      //表单数据
      ruleForm.value = assign({}, {...defaultForm, type: props.type });
    } else {
      //表单数据
      ruleForm.value = pick({ ...defaultForm, ...row }, keys(defaultForm));
      ruleForm.value.type = props.type;
    }
  });
};

// 方法：关闭窗口
const handleClose = () => {
  formRef.value?.resetForm();
  dialogVisible.value = false;
};
// 方法：提交表单
const submit = async () => {
  const params = { ...ruleForm.value, type: props.type};
  if (isUpdate.value) {
    await cloudfuncApi.updateMetaFunc(props.region,params);
    $app.$message.success("修改成功");
  } else {
    await cloudfuncApi.createMetaFunc(props.region,params);
    $app.$message.success("新增成功");
  }
  emit("reload");
  handleClose();
};
//初始化站点信息
onMounted(async () => {});
// 事件声明
const emit = defineEmits(["reload"]);
// 接口暴露
defineExpose({ openDialog });
</script>

<style lang="scss">
.offline-dataset-edit-table {
  height: calc(100vh - 390px);
  .table-page-wrapper {
    .table-wrapper {
      height: calc(100% - 50px) !important;
      .el-table {
        height: 100%;
      }
    }
  }
}
</style>
