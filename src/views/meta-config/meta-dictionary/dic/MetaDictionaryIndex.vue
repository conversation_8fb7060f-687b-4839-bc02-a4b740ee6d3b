<template>
  <div class="meta-dictionary-main">
    <el-splitter>
      <el-splitter-panel size="375px">
        <MetaDictionaryTree
          ref="treeRef"
          :treeData="treeData"
          title="词典类型"
          type="dict"
          :areaInfo="areaInfo"
          @updateTree="getTreeData"
          @updateTable="getTableData"
          operationAuth="/base/#/meta-dict/edit"
        />
      </el-splitter-panel>
      <el-splitter-panel>
        <MetaDictionaryTable ref="tableRef" :treeNode="treeNode" :treeData="treeData" @updateTree="getTreeData" />
      </el-splitter-panel>
    </el-splitter>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from "vue";
import useStore from "@/store";
import MetaDictionaryTable from "./MetaDictionaryTable.vue";
import useCtx from "@/hooks/useCtx";
import MetaDictionaryTree from "../common/MetaDictionaryTree.vue";
import * as metaWordDbApi from "@/api/meta-word";
import { computed } from "vue";
const { word } = useStore();
const treeRef = ref();
const tableRef = ref();

const treeNode = computed(() => treeRef.value?.getCurrentNode());
const { $route } = useCtx();
let treeData = ref<any>([]);
const areaInfo = computed(() => {
  return (treeRef.value?.areaOptions || []).find((item) => item.code == word.area) || {};
});
const getTreeData = async (key?: any) => {
  const filterText = treeRef.value.filterText;
  const params = { name: filterText };
  const treeRes: any = await metaWordDbApi.getTree("word", params, word.area);
  treeData.value =
    treeRes?.data.map((item: any, index: number) => {
      return {
        ...item,
        index: index,
      };
    }) || [];
  treeRef.value.setCurrentKey(key || treeData.value[0].id);
};

const getTableData = () => {
  tableRef.value && tableRef.value.loadList();
};
</script>
<style scoped lang="scss"></style>
