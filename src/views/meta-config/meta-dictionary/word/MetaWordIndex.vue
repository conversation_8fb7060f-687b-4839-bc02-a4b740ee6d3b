<template>
  <div class="meta-word-main">
    <el-splitter>
      <el-splitter-panel size="375px">
        <MetaDictionaryTree
          ref="treeRef"
          :treeData="treeData"
          title="字典类型"
          type="word"
          :areaInfo="areaInfo"
          :areaType="areaType"
          @updateTree="getTreeData"
          @updateTable="getTableData"
          @updateAreaType="changeAreaType"
          :showAreaType="showAreaType"
          operationAuth="/base/#/meta-word/edit"
        />
      </el-splitter-panel>
      <el-splitter-panel>
        <MetaWordTable ref="wordTableRef" :treeNode="treeNode" :treeData="treeData" @updateTree="getTreeData" />
      </el-splitter-panel>
    </el-splitter>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from "vue";
import useStore from "@/store";
import MetaDictionaryTable from "./MetaDictionaryTable.vue";
import useCtx from "@/hooks/useCtx";
import MetaWordTable from "./MetaWordTable.vue";
import MetaDictionaryTree from "../common/MetaDictionaryTree.vue";
import * as metaWordDbApi from "@/api/meta-word";
import { computed } from "vue";
import * as configJs from "../common/index";
const { word } = useStore();
const treeRef = ref();
const wordTableRef = ref();
const areaType = ref(1);
const areaList = ref([]);
const treeNode = computed(() => treeRef.value?.getCurrentNode());
const { $route } = useCtx();
const areaInfo = computed(() => {
  return (treeRef.value?.areaOptions || []).find((item) => item.code == word.area) || {};
});
let treeData = ref<any>([]);
const showAreaType = computed(() => areaInfo.value.envType == 1);
const getTreeData = async (key?: any) => {
  const filterText = treeRef.value.filterText;

  const params = { name: filterText };
  let area = word.area;
  if (areaInfo.value.envType == 1) {
    params.type = areaType.value;
  }
  if (areaType.value == 1) {
    area = treeRef.value?.areaOptions.find((item) => item.envType == 1)?.code || word.area;
  }
  const treeRes: any = await metaWordDbApi.getTree("dict", params, area);
  treeData.value =
    treeRes?.data.map((item: any, index: number) => {
      return {
        ...item,
        index: index,
      };
    }) || [];
  treeRef.value.setCurrentKey(key || treeData.value?.[0]?.id);
};
const getTableData = () => {
  wordTableRef.value && wordTableRef.value.loadList();
};
const changeAreaType = (val) => {
  areaType.value = val;
};
const handleAreaTypeChange = (val) => {
  getTreeData();
};
</script>
<style scoped lang="scss"></style>
